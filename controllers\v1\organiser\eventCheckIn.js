const sendOtp = require('../../../utils/sendOtp');
const EventOtpModel = require('../../../models/eventOtp');
const CheckInModel = require('../../../models/checkIn');
const UserModel = require('../../../models/user-model');
const LastAttendedEventModel = require('../../../models/lastAttendedEvent');
const sendWhatsappMessage = require('../../../utils/sendWhatsappMessage');
const schedule = require('node-schedule');
const axios = require('axios');
const AWS = require('aws-sdk');
const AdmZip = require("adm-zip");
const archiver = require('archiver');
const { PassThrough } = require('stream');
const imageSegregationStatusModel = require('../../../models/image-segregation-status');
const AttendeeCheckedInModel = require('../../../models/attendee-checked-in');
const { removeWhiteSpaceFromNumber } = require('../../../utils/utility');
const { FCMMessaging } = require('../../../utils/firebase-notification');
const Notification = require('../../../models/notifications');



// AWS S3 Configuration
const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_NEW_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_DEFAULT_REGION,
});

function getTomorrow8AM() {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1); // Move to the next day
    tomorrow.setHours(8, 0, 0, 0); // Set time to 8:00 AM
    return tomorrow;
}

function getThreeDaysLater8AM() {
    const threeDaysLater = new Date();
    threeDaysLater.setDate(threeDaysLater.getDate() + 3); // Move to 3 days later
    threeDaysLater.setHours(8, 0, 0, 0); // Set time to 8:00 AM
    return threeDaysLater;
}


exports.otp = async (req, res) => {
    const { mobileNumber } = req.body;
    try{
        const existing = await EventOtpModel.findOneAndDelete({mobileNumber});

        const result = await sendOtp(mobileNumber);
        if(!result.success){
            return res.status(400).json({
                status: false,
                message: 'Try again'
            })
        }

        const newEntry = new EventOtpModel({mobileNumber, otp: result.otp})
        await newEntry.save();
        return res.status(200).json({
            status: true,
            message: 'Otp send successfully.',
        })
    }catch(error){
        return res.send(error.message)
    }
}

exports.verifyOtp = async (req, res) => {
  const { mobileNumber, otp, eventOtp } = req.body;
  
  try{
      if(!mobileNumber || !otp || !eventOtp){
          return res.status(400).json({
              status: false,
              message: 'Please try again'
          })
      }
      const result = await EventOtpModel.findOne({ mobileNumber, otp });

      if(otp == eventOtp){
        await EventOtpModel.deleteMany({ mobileNumber });
        return res.status(200).json({
          status: true,
          message: 'Otp verified successfully'
        });
      }

      if(result){
        await EventOtpModel.deleteMany({ mobileNumber });
        return res.status(200).json({
          status: true,
          message: 'Otp verified successfully'
        });
      }else{
        return res.status(404).json({
          status: false,
          message: 'Invalid Otp'
        })
      }

  }catch(error){
      return res.status(404).json({
          status: false,
          message: error.message
      })
  }
}

exports.checkIn = async (req, res) => {
    const { name, email, mobile, company, designation, eventName, eventUuid } = req.body;

    try {
        const newCheckIn = new CheckInModel({ name, email, mobile, company, designation, eventName });
        const existUser = await UserModel.findOne({ mobileNumber: mobile });
        const templateName = 'download_app_reminder';
        const totalNumberOfUsers = await UserModel.countDocuments(); // Optimized count

        if (existUser) {
            // Mark invitations as already sent for existing users
            newCheckIn.tenMinuteInvitationSent = true;
            newCheckIn.oneDayInvitationSent = true;
            newCheckIn.threeDayInvitationSent = true;
        } else {
            // Schedule WhatsApp message after 10 minutes
            if (!newCheckIn.tenMinuteInvitationSent) {
                schedule.scheduleJob(new Date(Date.now() + 10 * 60 * 1000), async () => {
                    if (!newCheckIn.tenMinuteInvitationSent) {
                        await sendWhatsappMessage(mobile, templateName, [name, eventName, totalNumberOfUsers]);
                        newCheckIn.tenMinuteInvitationSent = true;
                        await newCheckIn.save();
                    }
                });
            }

            // Schedule WhatsApp message for next day at 8 AM
            if (!newCheckIn.oneDayInvitationSent) {
                const tomorrow8AM = getTomorrow8AM(); // Set time to 8:00 AM

                schedule.scheduleJob(tomorrow8AM, async () => {
                    if (!newCheckIn.oneDayInvitationSent) {
                        await sendWhatsappMessage(mobile, templateName, [name, eventName, totalNumberOfUsers]);
                        newCheckIn.oneDayInvitationSent = true;
                        await newCheckIn.save();
                    }
                });
            }

            // Schedule WhatsApp message for 3 days later at 8 AM
            if (!newCheckIn.threeDayInvitationSent) {
                const threeDaysLater8AM = getThreeDaysLater8AM(); // Set time to 8:00 AM

                schedule.scheduleJob(threeDaysLater8AM, async () => {
                    if (!newCheckIn.threeDayInvitationSent) {
                        await sendWhatsappMessage(mobile, templateName, [name, eventName, totalNumberOfUsers]);
                        newCheckIn.threeDayInvitationSent = true;
                        await newCheckIn.save();
                    }
                });
            }
        }

        await newCheckIn.save();

        const existingLastAttendee = await LastAttendedEventModel.findOne({ mobileNumber:  mobile});

        if(existingLastAttendee){
            existingLastAttendee.eventUuid = eventUuid;
            await existingLastAttendee.save();
        }else{
            const newLastAttendee = new LastAttendedEventModel({ mobileNumber: mobile, eventUuid });
            await newLastAttendee.save();
        }

        res.status(201).json({
            status: true,
            message: "User checked in successfully"
        });

    } catch (error) {
        res.status(400).json({
            status: false,
            message: error.message
        });
    }
};

exports.existingCheckInUser = async (req, res) => {
    const { mobile, eventID, userID } = req.body;
    try{
        if(!mobile){
            return res.status(400).json({
                status: false,
                message: 'Mobile Number is required'
            })
        }

        const checkExistingAttendee = await axios.post('https://api.klout.club/api/all-attendee-by-eventid', { user_id: userID, event_id: eventID, phone_number: mobile }, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const existingAttendee = checkExistingAttendee.data.data;
        if(existingAttendee.length > 0){
            const details = [{
                name: existingAttendee[0].first_name + " " + existingAttendee[0].last_name,
                email: existingAttendee[0].email_id,
                designation: existingAttendee[0].job_title,
                company: existingAttendee[0].company_name
            }]

            return res.status(200).json({
                status: true,
                data: details,
                message: 'Existing User'
            })
        }

        const existing = await CheckInModel.find({ mobile }).sort({ createdAt: -1 });

        if(!existing){
            return res.status(400).json({
                status: false,
                message: 'No existing user'
            })
        }

        return res.status(200).json({
            status: true,
            data: existing,
            message: 'Existing User'
        });

    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message
        })
    }
}

exports.sendWhatsapp = async (req, res) => {
    const { mobileNumber, templateName, bodyValues } = req.body;
    try {

        const result = await sendWhatsappMessage(mobileNumber, templateName, [...bodyValues]);

        if (!result || !result.data) {
            return res.status(400).json({
                status: false,
                message: 'Failed to send WhatsApp message, no response received from the server'
            });
        }

        if (result.data.result) {
            return res.status(200).json({
                status: true,
                message: 'Message sent successfully',
                messageID: result.data.id
            });
        }

        return res.status(400).json({
            status: false,
            message: 'Failed to send WhatsApp message, please try again'
        });

    } catch (error) {

        return res.status(404).json({
            status: false,
            message: error.message
        });
    }
};


exports.findLastAttendee = async (req, res) => {
    const mobileNumber = req.body.mobileNumber
    try{
      const lastAttendee = await LastAttendedEventModel.findOne({ mobileNumber });

      if(!lastAttendee){
        return res.status(200).json({
            status: false,
            message: 'Invlid Attendee!',
            data: null
        })
      }

      return res.status(200).json({
        status: true,
        data: lastAttendee,
        message: 'Attendee Found!'
      })
    }catch(error){
        return res.status(500).json({
            status: false,
            message: error.message
        })
    }
  }


  exports.updateLastAttendee = async (req, res) => {

    try{
        const { mobileNumber, eventUuid } = req.body;

        const lastAttendee = await LastAttendedEventModel.findOne({ mobileNumber });

        if(lastAttendee){
            lastAttendee.eventUuid = eventUuid;
            lastAttendee.save();
            return res.status(200).json({
                status: true,
                message: 'Last Attendee updated successfully'
            });
        }else{
            const newLastAttendee = new LastAttendedEventModel({ mobileNumber, eventUuid });
            await newLastAttendee.save();

            return res.status(200).json({
                status: true,
                message: 'New Attendee Created successfully'
            })
        }

        return res.status(404).json({
            status: false,
            message: 'Last Attendee not found'
        });
    }catch(error){
        return res.status(500).json({
            status: false,
            message: error.message
        })
    }
    
}

exports.findEventAttendeeProfileUrl = async (req, res) => {
    try {
      const { mobileNumbers } = req.body;
  
      if (!mobileNumbers || !Array.isArray(mobileNumbers)) {
        return res.status(400).json({
          status: false,
          message: "Invalid request. 'mobileNumbers' must be an array.",
        });
      }
  
      // Create a ZIP archive
      const archive = archiver("zip", { zlib: { level: 9 } });
      res.setHeader("Content-Type", "application/zip");
      res.setHeader(
        "Content-Disposition",
        "attachment; filename=profile_images.zip"
      );
  
      // Pipe the archive data to the response
      archive.pipe(res);
  
      // Iterate through the mobile numbers and fetch images
      for (let mobileNumber of mobileNumbers) {
        const validUser = await UserModel.findOne({ mobileNumber });
  
        if (validUser && validUser.profileImage) {
          try {
            // Fetch the file from S3
            const s3Params = {
              Bucket: process.env.AWS_BUCKET_NAME, // Your S3 bucket name
              Key: validUser.profileImage, // Image path/key in S3
            };
  
            const s3Object = await s3.getObject(s3Params).promise();
  
            // Generate file name using the _id and preserve the file extension
            const fileExtension = validUser.profileImage.split('.').pop();
            const fileName = `${validUser._id}.${fileExtension}`;
  
            // Append the image to the ZIP with the new file name
            archive.append(s3Object.Body, { name: fileName });
          } catch (s3Error) {
            console.error(`Error fetching file for ${mobileNumber}:`, s3Error);
          }
        }
      }
  
      // Finalize the archive
      archive.finalize();
  
    } catch (error) {
      console.error("Error creating ZIP file:", error);
      return res.status(500).json({
        status: false,
        message: error.message,
      });
    }
  };

// image segregation status controller
exports.changeImageSegregationStatus = async (req, res) => {
    const { eventUUID, status, finalStatus, message } = req.body;
    try{
        const existingSegregationStatus = await imageSegregationStatusModel.findOne({ eventUUID });

        if(existingSegregationStatus){
            existingSegregationStatus.status = status;
            existingSegregationStatus.finalStatus = finalStatus;
            existingSegregationStatus.message = message;
            await existingSegregationStatus.save();

            return res.status(200).json({
                status: true,
                message: 'Status is updated successfully'
            });
        }

        const newSegregationStatus = new imageSegregationStatusModel({ eventUUID, status, finalStatus, message });
        await newSegregationStatus.save();

        return res.status(201).json({
            status: true,
            message: 'New Segregation status is created successfully'
        })

    }catch(error){

        return res.status(500).json({
            status: false,
            message: error.message
        })

    }
}

exports.getImageSegregationStatus = async (req, res) => {
    const { eventUUID } = req.body;
    try{
        const segregationStatus = await imageSegregationStatusModel.findOne({ eventUUID });
        if(!segregationStatus){
            return res.status(404).json({
                status: false,
                message: 'No segregation status is found against this event'
            });
        }

        return res.status(200).json({
            status: true,
            data: segregationStatus,
            message: 'Segregation status is found successfully'
        });
    }catch(error){

        return res.status(500).json({
            status: false,
            message: error.message
        });

    }
}


exports.unzipFolder = async (req, res) => {
    const bucketName = process.env.AWS_PHOTO_BUCKET_NAME;
    const { eventUUID, zipFilePath, targetFolder } = req.body;
  
    if (!eventUUID || !zipFilePath || !targetFolder) {
      return res.status(400).json({
        status: false,
        message: "Missing required parameters.",
      });
    }
  
    // Send immediate response
    res.status(200).json({
      status: true,
      message: "Unzipping process started. You will be notified upon completion.",
    });
  
    // Background processing
    (async () => {
      try {
        const zipFile = await s3
          .getObject({
            Bucket: bucketName,
            Key: zipFilePath,
          })
          .promise();
  
        const zip = new AdmZip(zipFile.Body);
        const zipEntries = zip.getEntries();
  
        for (const entry of zipEntries) {
          if (!entry.isDirectory) {
            const fileName = entry.entryName;
            const fileData = entry.getData();
  
            await s3
              .putObject({
                Bucket: bucketName,
                Key: `${targetFolder}/${fileName}`,
                Body: fileData,
              })
              .promise();
          }
        }
  
        await imageSegregationStatusModel.findOneAndUpdate(
          { eventUUID },
          { status: 'success', finalStatus: true, message: 'Unzip Successfully' },
          { upsert: true, new: true }
        );
      } catch (error) {
        await imageSegregationStatusModel.findOneAndUpdate(
          { eventUUID },
          { status: 'error', finalStatus: false, message: 'Error occurred while unzipping folder' },
          { upsert: true, new: true }
        );
      }
    })();
};

exports.groupingPhoto = async (req, res) => {
    const { eventUUID } = req.body;

    if (!eventUUID) {
        return res.status(400).json({
            status: false,
            message: 'eventUUID is required',
        });
    }

    try {
        // Update segregation status
        const segregationStatus = await imageSegregationStatusModel.findOneAndUpdate(
            { eventUUID },
            {
                status: 'success',
                finalStatus: true,
                message: 'Grouping In Process',
            },
            { upsert: true, new: true }
        );

        res.status(200).json({
            status: true,
            message: 'Grouping is in process',
        });

        axios
            .post(
                'https://photo.klout.club/group-photo',
                {
                    event_uuid: eventUUID
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            )
            .then(() => {
                console.log('Python API request was successful.');
            })
            .catch((error) => {
                console.error(
                    'Error while calling the Python API:',
                    error.response ? error.response.data : error.message
                );
            });
    } catch (error) {
        console.error('Error in groupingPhoto API:', error.stack || error.message);
        res.status(500).json({
            status: false,
            message: 'Something went wrong while processing your request.',
        });
    }
};

exports.getEventPhotoFolders = async (req, res) => {
    const { eventUUID } = req.body;
    try {
      const bucketName = process.env.AWS_PHOTO_BUCKET_NAME;
      const prefix = `${eventUUID}/`;
  
      const params = {
        Bucket: bucketName,
        Prefix: prefix,
        Delimiter: '/',
      };
  
      const data = await s3.listObjectsV2(params).promise();
  
      const folderIds = data.CommonPrefixes
        .map(folder => folder.Prefix.split('/').slice(-2, -1)[0])
        .filter(id => id !== "Group" && id !== "Unknown");
  
      const users = await UserModel.find(
        { _id: { $in: folderIds } },
        { _id: 1, first_name: 1, last_name: 1 }
      ).lean();


      const objectIdToName = users.reduce((acc, user) => {
        acc[user._id] = user.first_name + ' ' + user.last_name;
        return acc;
      }, {});
  
      const folders = data.CommonPrefixes.map(folder => {
        const objectId = folder.Prefix.split('/').slice(-2, -1)[0];
        if (objectId === "Group" || objectId === "Unknown") {
          return {
            name: objectId,
            id: objectId,
            path: folder.Prefix,
          };
        }
        return {
          name: objectIdToName[objectId] || "Unnamed Folder",
          id: objectId || objectIdToName[objectId],
          path: folder.Prefix,
        };
      });
  
      return res.status(200).json({
        status: true,
        folders,
        message: "All Folders",
      });
    } catch (error) {
      res.status(500).json({
        status: false,
        message: error.message,
      });
    }
};


exports.getEventPhotos = async (req, res) => {
  const { eventUUID, userID } = req.body;
  try {
    const bucketName = process.env.AWS_PHOTO_BUCKET_NAME;
    const prefix = `${eventUUID}/${userID}/`;

    const params = {
      Bucket: bucketName,
      Prefix: prefix,
      Delimiter: '/',
    };

    const data = await s3.listObjectsV2(params).promise();

    const files = data.Contents.filter((file) => file.Key !== prefix).map((file) => ({
      name: file.Key.split('/').pop(),
      path: file.Key,
      url: s3.getSignedUrl('getObject', {
        Bucket: bucketName,
        Key: file.Key,
        Expires: 60 * 60,
      }),
    }));

    res.status(200).json({ 
        status: true,
        files,
        message: 'All Photos'
     });
  } catch (error) {
    res.status(500).json({ 
        status: false,
        message: error.message
     });
  }
};

exports.createAttendeeCheckedIn = async (req, res) => {
    const { mobileNumber, eventUUID, eventTitle, status, awardWinner, eventImageUrl } = req.body;
  
    try {
      const existingAttendee = await AttendeeCheckedInModel.findOne({ mobileNumber, eventUUID });
  
      if (existingAttendee) {
        const updatedAttendee = await AttendeeCheckedInModel.findOneAndUpdate(
          { mobileNumber, eventUUID },
          { eventTitle, checkInTime: Date.now() },
          { new: true, runValidators: true }
        );
  
        return res.status(200).json({
          status: true,
          message: 'Updated existing checked-in attendee',
          updatedAttendee
        });
      } else {
        const newCheckInAttendee = new AttendeeCheckedInModel({
          mobileNumber,
          eventUUID,
          eventTitle,
          status,
          awardWinner,
          eventImageUrl
        });
  
        await newCheckInAttendee.save();
  
        return res.status(201).json({
          status: true,
          message: 'Created checked-in attendee successfully',
          newCheckInAttendee
        });
      }
    } catch (error) {
      return res.status(404).json({
        status: false,
        message: error.message
      });
    }
};

exports.updateAttendeeCheckedIn = async(req, res) => {
  const { mobileNumber, eventUUID, awardWinner } = req.body;
  try{
    if(!mobileNumber || !eventUUID || !awardWinner ){
      return res.status(200).json({
        status: false,
        message: 'Invalid Input values'
      })
    }
    const updateExisting = await AttendeeCheckedInModel.findOneAndUpdate({ mobileNumber, eventUUID }, { awardWinner });

    if(!updateExisting){
      return res.status(200).json({
        status: false,
        message: 'Record cannot find'
      });
    }

    return res.status(200).json({
      status: true,
      message: 'Record update successfully'
    });
    
  }catch(error){

    return res.status(200).json({
      status: false,
      message: error.message
    })

  }
}


// this funciton is for if all the photos is update and process successfully then it will trigger to the notification to all the users which are checkedin
exports.sendBulkPhotoNotification = async (req, res) => {
  const { checkInUsersNumbers, eventUuid, eventTitle } = req.body;
  try{
    for(let eachUserNumber of checkInUsersNumbers){
      console.log(eachUserNumber)
      const userNumber = removeWhiteSpaceFromNumber(eachUserNumber);
      console.log(userNumber);

      const isUser = await UserModel.findOne({ mobileNumber: userNumber });

      if(!isUser){
        return res.status(404).json({
          status: false,
          message: 'User not found'
        });
      }

      const notification =  new Notification({
        user_id: isUser._id,
        from_user_id: '66b4c728ab20f1b3ad4497f8',
        title: `Pictures - ${eventTitle}`,
        body: `The event pictures for the ${eventTitle} have been updated`,
        data: JSON.stringify({Type: "6",
          Title: `Pictures - ${eventTitle}`,
          Body: `The event pictures for the ${eventTitle} have been updated`,
          eventUuid: eventUuid,
          Name: `Pictures - ${eventTitle}`,
          ProfileImage: 'klout/uploads/users/profile_images/af69d220-886d-4303-ae1c-c58e1bed9859.png'
        }),
        isRead: 0,
        type: 6
      });

      await notification.save();


      // Prepare FCM message
      const FCMMessage = {
        message: {
          token: isUser.deviceToken,
          notification: {
            title: `Pictures - ${eventTitle}`,
            body: `The event pictures for the ${eventTitle} have been updated`,
          },
          data: {
            Type: "6",
            Title: `Pictures - ${eventTitle}`,
            Body: `The event pictures for the ${eventTitle} have been updated`,
            eventUuid: eventUuid,
            Name: `Pictures - ${eventTitle}`,
          },
        }
      };

      // Send notification using FCM messaging function
      await FCMMessaging(FCMMessage);
    }

    return res.status(200).json({
      status: true,
      message: 'send all notification'
    });
  }catch(error){
    return res.status(500).json({
      status: false,
      message: error.message
    });
  }
}



// async function updateEventImageUrl() {
//   try {
//     const allCheckedInUser = await AttendeeCheckedInModel.find({});

//     for (let eachUser of allCheckedInUser) {
//       const eachUserEvent = eachUser.eventUUID;

//       try {
//         const response = await axios.post(`https://api.klout.club/api/displayEvent/${eachUserEvent}`);
//         console.log(response)

//         if (response.data?.data?.image) {
//           const imageUrl = response.data.data.image;
//           eachUser.eventImageUrl = imageUrl;
//           await eachUser.save();
//           console.log(`Updated user with eventUUID: ${eachUserEvent}`);
//         }
//       } catch (err) {
//         console.warn(`Failed to fetch event for UUID ${eachUserEvent}: ${err.message}`);
//         // optionally: add some delay if 429 happens too often
//       }
//     }
//   } catch (error) {
//     console.error('Error while updating event image URLs:', error.message);
//   }
// }


// updateEventImageUrl();



// function sleep(ms) {
//   return new Promise(resolve => setTimeout(resolve, ms));
// }

// async function updateEventImageUrl() {
//   try {
//     const allCheckedInUser = await AttendeeCheckedInModel.find({});

//     for (let eachUser of allCheckedInUser) {
//       const eachUserEvent = eachUser.eventUUID;

//       try {
//         const response = await axios.post(`https://api.klout.club/api/displayEvent/${eachUserEvent}`);

//         if (response.data?.data?.image) {
//           const imageUrl = response.data.data.image;
//           eachUser.eventImageUrl = imageUrl;
//           await eachUser.save();
//           console.log(`Updated user with eventUUID: ${eachUserEvent}`);
//         }
//       } catch (err) {
//         console.warn(`Failed for UUID ${eachUserEvent}: ${err.message}`);
//       }

//       // Wait 3 seconds before processing the next user
//       await sleep(500);
//     }
//   } catch (error) {
//     console.error('Error while updating event image URLs:', error.message);
//   }
// }

// updateEventImageUrl()