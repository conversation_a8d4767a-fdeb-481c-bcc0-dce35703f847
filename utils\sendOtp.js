
const textLocalService = require('./sendSmsTextLocal');
const sendOtpWhatsapp = require('../controllers/v1/sendOtpWhatsapp');


const sendOtp = async (mobileNumber) => {

const otp = Math.floor(100000 + Math.random() * 900000);
  try {
    const response = await textLocalService(mobileNumber, otp);
    await sendOtpWhatsapp(mobileNumber, otp);

    if (response.status === 'success') {
      
      return { success: true, message: 'OTP sent successfully', otp };  
    } else {
        console.log(response)
      return { success: false, message: 'Failed to send OTP' };
    }
  } catch (error) {
    throw new Error('Error sending OTP');
  }
};

module.exports = sendOtp;
