const UserChatModel = require('../../../models/user-chat-model');
const ChatTrackView = require('../../../models/chat-track-view');
const UserModel = require('../../../models/user-model');
const { FCMMessaging } = require('../../../utils/firebase-notification');
const mongoose = require('mongoose');
const moment = require("moment");
const firebaseDb = require("../../../services/firebase");
const AttendeeCheckedInModel = require("../../../models/attendee-checked-in");


exports.messageByOrgainser = async (req, res) => {
    try {
      const adminNumber = 9643314331;
      const admin = await UserModel.findOne({ mobileNumber: adminNumber });
      if (!admin) {
        return res.status(404).json({ message: 'Admin not found' });
      }
  
      const adminId = admin._id;
      const adminMessageTitle = req.body.title;
      const adminMessage = req.body.message;
      const recieversNumber = [...req.body.numbers];
  
      const results = [];
      for (const reciever of recieversNumber) {
        const user = await UserModel.findOne({ mobileNumber: reciever });
        if (user) {
          const userId = user._id;
          const result = sendMessage(adminId, userId, adminMessageTitle, adminMessage);
          results.push(result);
        } else {
          results.push({ reciever, success: false, message: "User not found" });
        }
      }
  
      return res.status(200).json({ message: 'Messages sent successfully', details: results });
    } catch (error) {
      console.error(error);
      return res.status(500).json({ message: 'Failed to send messages', error: error.message });
    }
  };
  
const sendMessage = async (from, to, title, message) => {
  try {
    const fromUserId = from;
    const toUserId = to;

    if (!fromUserId || !toUserId || !message || !title) {
      throw new Error("fromUserId, toUserId, title, and message are required!");
    }

    let connectionId;

    // Check for existing chat
    const existingChat = await UserChatModel.findOne({ fromUserId, toUserId });
    if (existingChat) {
      connectionId = existingChat.connectionId;
    }

    if (!connectionId) {
      connectionId = new mongoose.Types.ObjectId().toString();
    }

    let chatData;
    const isExistData = await UserChatModel.findOne(
      { connectionId, fromUserId, toUserId },
      { __v: false }
    ).sort({ createdAt: -1 });

    if (isExistData) {
      const unreadCount = isExistData.unreadCount + 1;

      await UserChatModel.updateOne(
        { connectionId, fromUserId, toUserId },
        {
          $set: {
            lastMessage: message,
            lastMessageDateTime: moment().format(),
            isDeleted: 0,
            unreadCount,
          },
        }
      );

      chatData = { connectionId, lastMessage: message };

      const chatTrack = await ChatTrackView.findOne({
        from_user_id: fromUserId,
        to_user_id: toUserId,
      });

      if (chatTrack) {
        chatTrack.ModifiedDate = new Date();
        await chatTrack.save();
      }
    } else {
      const newChat = new UserChatModel({
        connectionId,
        fromUserId,
        toUserId,
        lastMessage: message,
        lastMessageDateTime: moment().format(),
        unreadCount: 1,
        muteStatus: 0,
        pinedStatus: 0,
        isDeleted: 0,
      });
      chatData = await newChat.save();

      const ChatTrackViewData = new ChatTrackView({
        from_user_id: fromUserId,
        to_user_id: toUserId,
        connectionId,
        chatStatus: 1,
        ModifiedDate: new Date(),
      });
      await ChatTrackViewData.save();
    }

    const isExistDataReverse = await UserChatModel.findOne(
      { connectionId, fromUserId: toUserId, toUserId: fromUserId },
      { __v: false }
    );

    if (isExistDataReverse) {
      const unreadCountReverse = isExistDataReverse.unreadCount + 1;

      await UserChatModel.updateOne(
        { connectionId, fromUserId: toUserId, toUserId: fromUserId },
        {
          $set: {
            unreadCount: unreadCountReverse,
            lastMessage: message,
            lastMessageDateTime: moment().format(),
            isDeleted: 0,
          },
        }
      );
    } else {
      const newChatReverse = new UserChatModel({
        connectionId,
        fromUserId: toUserId,
        toUserId: fromUserId,
        lastMessage: message,
        lastMessageDateTime: moment().format(),
        unreadCount: 1,
        muteStatus: 0,
        pinedStatus: 0,
        isDeleted: 0,
      });
      await newChatReverse.save();
    }

    const userToData = await UserModel.findOne({ _id: toUserId });
    if (userToData && userToData.deviceToken) {
      const FCMMessage = {
        message: {
          token: userToData.deviceToken,
          notification: {
            title,
            body: message,
          },
          data: {
            Type: "4",
            Title: title,
            Body: message,
            ConnectionId: connectionId,
            RequesterId: fromUserId,
            Name: title,
          },
        },
      };
      FCMMessaging(FCMMessage);
    }
    const firebaseFromUserId = fromUserId.toString();
    const firebaseToUserId = toUserId.toString();

    const firebasePath = `${firebaseFromUserId}_${firebaseToUserId}`;
    const payload = {
      date: moment().format("YYYY-MM-DD"),
      isRead: "0",
      message,
      replier_id: firebaseToUserId,
      sender_id: firebaseFromUserId,
      time: moment().format("YYYY-MM-DD HH:mm"),
      type: "text",
    };
  //   console.log(payload)

    firebaseDb.ref(firebasePath).push(payload);

    return { success: true, connectionId, message };
  } catch (error) {
    console.error(error);
    return { success: false, error: error.message };
  }
};

// this is the code for the users to know how many events did the uesrs attend

exports.getUserEventList = async(req, res) => {
  const { mobileNumber } = req.body;
  try{

    if(!mobileNumber){
      return res.status(404).json({
        status: false,
        message: 'Provide User Mobile Number'
      });
    }

    const allAttendedEvent = await AttendeeCheckedInModel.find({ mobileNumber });


    if(allAttendedEvent){
      console.log(allAttendedEvent);
    }

    return res.status(200).json({
      status: true,
      data: allAttendedEvent,
      message: 'All Attended Event List'
    })

  }catch(error){

    return res.status(500).json({
      status: false,
      message: error.message
    })

  }
}

  
