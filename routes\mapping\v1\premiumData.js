const express = require('express');
const app = express();
const router = express.Router();
const premiumDataController = require('../../../controllers/v1/mapping/premiumData');
const fileUpload = require('express-fileupload');

app.use(fileUpload());

app.use(express.urlencoded({ extended: false }))


router
    .post('/upload', premiumDataController.uploadPremiumData)
    .get('/all', premiumDataController.all)
    .patch('/update', premiumDataController.update)

    // update 10-03-2025 (to store the attendee from the php to mapping)
    .post('/attendee-upload',premiumDataController.AttendeeUpload)

module.exports = router