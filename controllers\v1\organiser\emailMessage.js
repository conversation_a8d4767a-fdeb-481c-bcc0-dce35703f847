const EmailMessageModel = require('../../../models/sendEmailMessage');


exports.addReciptForEmail = async(req, res) => {
    const { eventUUID, userID, firstName, templateName, messageID } = req.body;

    try{
        const recipt = new EmailMessageModel({ eventUUID, userID, firstName, templateName, messageID });
        await recipt.save();
        return res.status(201).json({
            status: true,
            message: 'Recipt added successfully'
        })
    }catch(error){
        return res.status(400).json({
            status: false,
            message: error.message
        })
    }
}

exports.viewFilteredForEmail = async(req, res) => {
    const templateName = req.body.templateName;
    const eventUUID = req.body.eventUUID;
    const userID = req.body.userID;

    try{
        const allMessagesRecipt = await EmailMessageModel
            .find({ templateName, eventUUID, userID })
            .populate({
                path: 'messageID',
                model: 'email_message_status',
                localField: 'messageID',
                foreignField: 'messageID'
            });;
        if(!templateName || !eventUUID || !userID){
            return res.status(400).json({
                status: false,
                message: 'Invalid Request'
            })
        }

        return res.status(200).json({
            status: true,
            data: allMessagesRecipt,
            message: 'All Messages Recipt'
        })

    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message
        })
    }
}