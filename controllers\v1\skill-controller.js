const { sendResponse } = require("../../utils/utility");
const SkillModel = require("../../models/skill-model");

const create = async (req, res) => {
  const { skill, skillDesc, status } = req.body;

  // Validate user input
  if (!skill) {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Skill is required.",
      data: exist,
    });
  }

  const exist = await SkillModel.findOne({ skill });

  if (exist) {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Skill Already Exist, Please try with other name",
      data: exist,
    });
  }

  const data = new SkillModel({
    skill: skill,
    skillDesc: skillDesc,
    status: status,
  });
  try {
    const result = await data.save();
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Skill added successfully!",
      data: result,
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: error.message,
      data: error,
    });
  }
};

const getlist = async (req, res) => {
  try {
    var limit = req.body.limit;
    var offset = req.body.offset;
    if (!limit) {
      limit = 10;
    }
    if (!offset) {
      offset = 0;
    }
    const data = await SkillModel.find({ status: 1 }, { __v: false });
     // .skip(offset)
     // .limit(limit);
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Skills list retrieved successfully",
      data: data,
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
      data: error,
    });
  }
};

const search = async (req, res) => {
  try {
    let query = {};
    const string = req?.body?.string;
    if (string) {
      query.skill = { $regex: string, $options: "i" };
    }
    query.status = 1;
    const data = await searchData(query);
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Skills list retrieved successfully.",
      data: data,
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
      data: error,
    });
  }
};

const details = async (req, res) => {
  try {
    const { id } = req.body;
    // Validate user input
    if (!id) {
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 400,
        msg: "skill is required!",
      });
    }
    const data = await SkillModel.findOne({ _id: id }, { __v: false });
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Skill  retrieved successfully",
      data: data,
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
      data: error,
    });
  }
};

const searchData = async (query) => {
  return await SkillModel.find(query, { __v: false });
};

module.exports = {
  create,
  getlist,
  search,
  details,
};
