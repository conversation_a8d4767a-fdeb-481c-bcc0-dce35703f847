const mongoose = require("mongoose");

const { Schema } = mongoose;

const userPointsSchema = new mongoose.Schema(
  {
    userId: {
      required: true,
      type: Schema.Types.ObjectId,
      ref: "user",
    },
    allocatedPoints: {
      type: Number,
      default: 0,
    },
    remainPoints: {
      type: Number,
      default: 0,
    },
    usedPoints: {
      type: Number,
      default: 0,
    },
    role: {
      required: true,
      type: String,
    },
    AllocatedPointsByUserId: {
      required: true,
      type: Schema.Types.ObjectId,
      ref: "user",
    },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
  }
);

module.exports = mongoose.model("user_point", userPointsSchema);
