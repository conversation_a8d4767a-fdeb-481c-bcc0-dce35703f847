const mongoose = require('mongoose');
const { Schema } = mongoose;

const connectionsSchema = new mongoose.Schema({
    from_user_id: {
        type: mongoose.Schema.ObjectId,
    },
    to_user_id: {
        type: mongoose.Schema.ObjectId,
    },

    /**
     * status :: 0:not;1:deleted
     */
    is_deleted: {
        type: String,
        required: false,
    },

}, {
    timestamps: {
        createdAt: 'createdAt', // Use `created_at` to store the created date
        updatedAt: 'updatedAt' // and `updated_at` to store the last updated date
    }
})

module.exports = mongoose.model('connections', connectionsSchema)