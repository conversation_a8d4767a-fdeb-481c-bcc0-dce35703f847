const mongoose = require("mongoose");

const { Schema } = mongoose;

const userChatMadeSchema = new mongoose.Schema(
  {
    userId: {
      required: true,
      type: Schema.Types.ObjectId,
      ref: "user",
    },
    contactUserId: {
      required: true,
      type: Schema.Types.ObjectId,
      ref: "user",
    },
    chatMade: {
      required: true,
      type: String,
    },
    points: {
      type: Number,
      default: 1,
    },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
  }
);

module.exports = mongoose.model("user_chat_made", userChatMadeSchema);
