const mongoose = require('mongoose');


const attendeeCheckedInSchema = new mongoose.Schema({
    mobileNumber: {
        type: Number,
        required: [true, 'Mobile Number is required'],
        match: [/^\+?[1-9]\d{1,14}$/, 'Please provide a valid mobile number'],
        index: true
    },
    eventUUID: {
        type: String,
        required: [true, 'Event uuid is required'],
        index: true
    },
    eventTitle: {
        type: String,
        required: [true, 'Event title is required']
    },
    status: {
        type: String,
        required: [true, 'Status is required']
    },
    checkInTime: {
        type: Date,
        required: [true, 'Checked in time is required'],
        default: Date.now
    },
    awardWinner: {
        type: Boolean,
        required: [true, 'Award winner is required']
    },
    eventImageUrl: {
        type: String,
        required: [true, 'Event Image Url is required']
    }
}, {timestamps: true});


// Add a unique compound index for mobileNumber and eventUUID
// attendeeCheckedInSchema.index({ mobileNumber: 1, eventUUID: 1 }, { unique: true });

module.exports = mongoose.model('attendee_checked_in', attendeeCheckedInSchema);