const express = require('express');
// const Model = require('../../models/skill-model');
const SkillController = require('../../controllers/v1/skill-controller');
const router = express.Router();
const auth = require("../../middleware/auth");
// const {sendResponse} = require("../../utils/utility");

router.post('/create', auth, async (req, res) => {
    const exist = await SkillController.create(req, res);
});

router.get('/getlist',  async (req, res) => {
    const exist = await SkillController.getlist(req, res);
});

router.post('/getlist',  async (req, res) => {
    const exist = await SkillController.getlist(req, res);
});

router.post('/search',  async (req, res) => {
    const exist = await SkillController.search(req, res);
});

router.post('/details',  async (req, res) => {
    const exist = await SkillController.details(req, res);
});

module.exports = router;