const mongoose = require('mongoose');

const premiumDataSchema = new mongoose.Schema({
    first_name: String,
    last_name: String,
    job_title: String,
    company_name: String,
    industry: String,
    email: String,
    phone_number: String,
    alternate_mobile_number: String,
    website: String,
    employee_size: String,
    company_turn_over: String,
    linkedin_page_link: String,
    country: String,
    job_function: String
}, {timestamps: true} );

module.exports = mongoose.model("premium_data", premiumDataSchema);