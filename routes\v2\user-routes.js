const express = require('express');
const UserModel = require('../../models/user-model');
const router = express.Router();
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const auth = require("../../middleware/auth");
const {sendResponse} = require("../../utils/utility");
//Post Method
router.post('/register', async (req, res) => {
    const { name, email, password } = req.body;

    // Validate user input
    if (!(email && password && name)) {
        return await sendResponse( req, res,{statusCode:400, msg : "email, name and password required!"})
    }

    const oldUser = await UserModel.findOne({ emailId : email });

    if (oldUser) {
      return await sendResponse( req, res,{statusCode:409, msg : "User Already Exist. Please Login"})
    }

    const encryptedPassword = await bcrypt.hash(password, 10);

    const data = new UserModel({
        name: req.body.name,
        emailId: req.body.email,
        mobileNumber: req.body.mobileNumber,
        profileImage: req.body.profileImage,
        company: req.body.company,
        designation: req.body.designation,
        industryName: req.body.industryName,
        industryId: req.body.industryId,
        location: req.body.location,
        linkedInId: req.body.linkedInId,
        linkedInAccessToken: req.body.linkedInAccessToken,
        deviceToken: req.body.deviceToken,
        deviceVersion: req.body.deviceVersion,
        deviceType: req.body.deviceType,
        deviceName: req.body.deviceName,
        password: encryptedPassword
    })

    try {
        const user = await data.save();
         // Create token
         const token = jwt.sign(
            { user_id: user._id, email },
            process.env.TOKEN_KEY,
            {
            expiresIn: "1h",
            }
        );
        // save user token
        return await sendResponse( req, res,{statusCode:200, msg : "User registered successfully", data : {user, token}})
    }
    catch (error) {
        return await sendResponse( req, res,{statusCode:400, msg : error.message, data : error})
    }
})

//Get all Method
router.get('/getAllUsers', async (req, res) => {
    try {
        const data = await UserModel.find();
        return await sendResponse( req, res,{statusCode:200, msg : "User list retrieved successfully", data})
    }
    catch (error) {
        return await sendResponse( req, res,{statusCode:500, msg : error.message})
    }
})

router.post("/login", async (req, res) => {
    try {
      // Get user input
      const { email, password } = req.body;
  
      // Validate user input
      if (!(email && password)) {;
       return await sendResponse( req, res,{statusCode:400, msg : "email and password is required"})
      }
      // Validate if user exist in our database
      const user = await UserModel.findOne({ emailId: email });
  
      if (user && (await bcrypt.compare(password, user.password))) {
        // Create token
        const token = jwt.sign(
          { user_id: user._id, email },
          process.env.TOKEN_KEY,
          {
            expiresIn: "1h",
          }
        );
        return await sendResponse( req, res,{statusCode:200, msg : "logged in successfully!", data: {user, token}})
      }
     return await sendResponse( req, res,{statusCode:400, msg : "email or password is not valid!"})
    } catch (err) {
      console.log(err);
    }
  });

router.get("/checklogin", auth, async (req, res) => {
    return await sendResponse( req, res,{statusCode:200, msg : "Welcome 🙌 to Klout APIs"})
});

/**
 * 
 */
router.post("/updateDeviceToken", async (req, res) => {
  try {
    // Get user input
    const { id, DeviceToken } = req.body;

    // Validate user input
    if (!(id && DeviceToken)) {;
     return await sendResponse( req, res,{statusCode:400, msg : "Token  is required"})
    }
    const result = await  UserModel.update({_id: id},{
      $set:{
        deviceToken: DeviceToken,
      }
    });
    return await sendResponse( req, res,{statusCode:200, msg : "Token updated successfully!", data: {result,DeviceToken}})
  } catch (err) {
    console.log(err);
  }
});

module.exports = router;