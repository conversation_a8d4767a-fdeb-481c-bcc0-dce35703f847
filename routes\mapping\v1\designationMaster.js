const express = require('express');
const router = express.Router();
const designationMasterController = require('../../../controllers/v1/mapping/designationMaster');


router
    .post('/add-designation' , designationMasterController.addDesignation)
    .patch('/update-designation/:id', designationMasterController.updateDesignation)
    .get('/view-designation/:id', designationMasterController.viewDesignation)
    .delete('/delete-designation/:id', designationMasterController.deleteDesignation)
    .get('/all-designation', designationMasterController.getAllDesignation)
    .post('/add-unmapped-designation', designationMasterController.addUnmappedDesignation)
    .post('/mapping-unmapped-designation', designationMasterController.mappingUnmappedDesignation)
    .get('/get-unmapped-designation', designationMasterController.getUnmappedDesignation)
    .delete('/delete-unmapped-designation/:id', designationMasterController.deleteUnmappedDesignation)
    .get('/get-user-designation', designationMasterController.getUserDesignations)



    .post('/add-unmapped-or-new-designation', designationMasterController.addUnmappedOrNewDesignation)
    .post('/get-all-unmapped-or-new-designation', designationMasterController.listAddUnmappedOrNewDesignation)
    .delete('/delete-unmapped-or-new-designation/:id', designationMasterController.deleteUnmappedOrNewDesignation)
    .post('/send-back-unmapped-to-unmapped-or-new-designation', designationMasterController.unmappedToUnmappedOrNewDesignation)
    .post('/send-back-new-designation-to-unmpapped-or-new-designation', designationMasterController.designationToUnmappedOrNewDesignation)
    .post('/unmapped-mapped-designation', designationMasterController.unmappedMappedDesignation)

module.exports = router