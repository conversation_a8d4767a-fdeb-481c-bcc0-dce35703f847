const PeopleModel = require('../../../models/people');
const PremiumDataModel = require('../../../models/permiumData');
const { removeWhiteSpaceFromNumber, trimToTenDigits } = require('../../../utils/utility');
const CompanyMasterModel = require('../../../models/companyMaster');
const axios = require('axios');


exports.addPeople = async (req, res) => {
    const { 
        firstName,
        lastName,
        linkedinUrl,
        designation,
        company,
        industry,
        city
        } = req.body
    try{

        if(!firstName || !lastName || !linkedinUrl || !designation || !company || !industry || !city){
            return res.status(404).json({
                status: false,
                message: 'Invalid Input'
            })
        }

        const existinInPremiumData = await PremiumDataModel.findOne({ linkedin_page_link: linkedinUrl });
        const existingInProfile = await PeopleModel.findOne({ linkedinUrl });
        console.log(existingInProfile)
        if(existinInPremiumData){
            const mobileNumber = removeWhiteSpaceFromNumber(existinInPremiumData.phone_number);
            const email = existinInPremiumData.email;
            if(existingInProfile){
                existingInProfile.email = email;
                existingInProfile.mobileNumber  = Number(mobileNumber);
                existingInProfile.firstName = firstName.toLowerCase();
                existingInProfile.lastName = lastName.toLowerCase();
                existingInProfile.designation = designation.toLowerCase();
                existingInProfile.company = company.toLowerCase();
                existingInProfile.industry = industry.toLowerCase();
                existingInProfile.city = city.toLowerCase();
                existingInProfile.save();

                return res.status(200).json({
                    status: true,
                    message: 'data is updated successfully'
                });
            }

            const newEntry = new PeopleModel({
                firstName: firstName.toLowerCase(),
                lastName: lastName.toLowerCase(),
                linkedinUrl: linkedinUrl,
                designation: designation.toLowerCase(),
                company: company.toLowerCase(),
                industry: industry.toLowerCase(),
                city: city.toLowerCase(),
                mobileNumber,
                email
            })

            await newEntry.save();

            return res.status(200).json({
                status: true,
                message: 'new entry is created'
            });
        }

        const existInPremiumDataUsingName = await PremiumDataModel.findOne({ first_name: firstName.toLowerCase(), last_name: lastName.toLowerCase(), company_name: company.toLowerCase()  })
        if(existInPremiumDataUsingName){
            const mobileNumber = removeWhiteSpaceFromNumber(existInPremiumDataUsingName.phone_number);
            const email = existInPremiumDataUsingName.email;
            if(existingInProfile){
                existingInProfile.email = email;
                existingInProfile.mobileNumber  = Number(mobileNumber);
                existingInProfile.firstName = firstName.toLowerCase();
                existingInProfile.lastName = lastName.toLowerCase();
                existingInProfile.designation = designation.toLowerCase();
                existingInProfile.company = company.toLowerCase();
                existingInProfile.industry = industry.toLowerCase();
                existingInProfile.city = city.toLowerCase();
                existingInProfile.save();

                return res.status(200).json({
                    status: true,
                    message: 'data is updated successfully'
                });
            }

            const newEntry = new PeopleModel({
                firstName: firstName.toLowerCase(),
                lastName: lastName.toLowerCase(),
                linkedinUrl: linkedinUrl,
                designation: designation.toLowerCase(),
                company: company.toLowerCase(),
                industry: industry.toLowerCase(),
                city: city.toLowerCase(),
                mobileNumber,
                email
            })

            await newEntry.save();

            return res.status(200).json({
                status: true,
                message: 'new entry is created'
            });
        }

        if(existingInProfile){
            existingInProfile.firstName = firstName.toLowerCase();
            existingInProfile.lastName = lastName.toLowerCase();
            existingInProfile.designation = designation.toLowerCase();
            existingInProfile.company = company.toLowerCase();
            existingInProfile.industry = industry.toLowerCase();
            existingInProfile.city = city.toLowerCase();

            await existingInProfile.save();

            return res.status(200).json({
                status: true,
                message: 'Entry is updated successfully'
            })

        }


        const newEntry = new PeopleModel({
            firstName: firstName.toLowerCase(),
            lastName: lastName.toLowerCase(),
            linkedinUrl: linkedinUrl,
            designation: designation.toLowerCase(),
            company: company.toLowerCase(),
            industry: industry.toLowerCase(),
            city: city.toLowerCase()
        })

        await newEntry.save();

        return res.status(200).json({
            status: true,
            message: 'new entry is created'
        });



    }catch(error){

        return res.status(500).json({
            status: false,
            message: error.message
        })

    }
};

exports.getAllPeople = async (req, res) => {
    console.log(req.query);

    const page = parseInt(req.query.page) || 1;
    const company = req.query.company || "";
    const industry = req.query.industry || "";
    const designation = req.query.designation || "";
    const city = req.query.city || "";
    const firstName = req.query.firstName || "";
    const lastName = req.query.lastName || "";

    const limit = 50;
    const skip = (page - 1) * limit;

    try {
        const query = {};

        if (company) {
            query.company = { $regex: company, $options: "i" };
        }

        if (industry) {
            query.industry = { $regex: industry, $options: "i" };
        }

        if (designation) {
            query.designation = { $regex: designation, $options: "i" };
        }

        if (city) {
            query.city = { $regex: city, $options: "i" };
        }

        if (firstName) {
            query.firstName = { $regex: firstName, $options: "i" };
        }

        if (lastName) {
            query.lastName = { $regex: lastName, $options: "i" };
        }


        const peoples = await PeopleModel
            .find(query)
            .limit(limit)
            .skip(skip)
            .select("firstName lastName linkedinUrl designation company industry city email mobileNumber");

        const totalPeople = await PeopleModel.countDocuments(query);

        // Return success response
        return res.status(200).json({
            status: true,
            data: { peoples, totalPeople },
            message: "All People",
        });
    } catch (error) {
        // Return error response
        return res.status(404).json({
            status: false,
            message: error.message,
        });
    }
};

exports.getSearchPeople = async (req, res) => {
    
    const designation = req.body.designation;
    const city = req.body.city;

    try {
        if(!designation || !city){
            return res.status(404).json({
                status: false,
                message: 'Invalid Input'
            });
        }
        const query = {};


        if (designation) {
            query.designation = { $regex: designation, $options: "i" };
        }

        if (city) {
            query.city = { $regex: city, $options: "i" };
        }



        const peoples = await PeopleModel
            .find(query);

        // Fetch employee size for each company in peoples array
        const peoplesWithCompanySize = await Promise.all(
            peoples.map(async (person) => {
                // Find the company in CompanyMasterModel
                const companyData = await CompanyMasterModel.findOne({
                company: person.company.toLowerCase(),
                }).select("companySize");
    
                // Add employee size if found
                return {
                ...person._doc,
                employeeSize: companyData ? companyData.companySize : "Not Available",
                };
            })
            );

        const totalPeople = await PeopleModel.countDocuments(query);

        // Return success response
        return res.status(200).json({
            status: true,
            data: { totalPeople, peoplesWithCompanySize },
            message: "All People",
        });
    } catch (error) {
        // Return error response
        return res.status(404).json({
            status: false,
            message: error.message,
        });
    }
};

exports.getContactInfoUsingRocketReach = async (req, res) => {
    const { linkedinUrl } = req.body;
    try{
        const targetPeople = await PeopleModel.findOne({ linkedinUrl });
        const response = await axios.get(`https://api.rocketreach.co/api/v2/person/lookup?linkedin_url=${linkedinUrl}`,{
            headers: {
                'Api-Key': '18912edk26c5a9839f9cd4271fe211ad60e5c2be'
            }
        });

        const contact = response.data;

        const professionalEmail = contact.recommended_email;

        let mobileNumber = '';

        if(contact.phones.length > 0){
            mobileNumber = trimToTenDigits(removeWhiteSpaceFromNumber(contact.phones[0].number));
        }

        if(targetPeople){
            if(professionalEmail !== null){
                if(!targetPeople.email){
                    
                    targetPeople.email = professionalEmail;
                }
            }
    
            if(mobileNumber !== ''){
                if(!targetPeople.mobileNumber){
                    targetPeople.mobileNumber = mobileNumber;
                }
            }

            await targetPeople.save();
        }

        return res.status(200).json({
            status: true,
            data: {
                email: professionalEmail,
                mobile: mobileNumber
            }
        })
    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message
        })
    }
}

exports.updateContactInfo = async (req, res) => {
    const { linkedinUrl, email, mobileNumber } = req.body;
    try{
        if(!linkedinUrl){
            return res.status(404).json({
                status: false,
                message: 'Invalid Input'
            })
        }
        const contact = await PeopleModel.findOne({ linkedinUrl });
        if(!contact){
            return res.status(200).json({
                status: false,
                message: 'Invalid linkedin url'
            });
        }

        const existingNumber = contact.mobileNumber;
        if(!existingNumber){
            contact.mobileNumber = mobileNumber;
        }

        const existingEmail = contact.email;
        if(!existingEmail){
            contact.email = email;
        }

        await contact.save();

        return res.status(200).json({
            status: true,
            message: 'Updated successfully'
        })
    }catch(error){
        return res.status(500).json({
            status: false,
            message: error.message
        })
    }
}


exports.getContactInfoUsingSignalHire = async (req, res) => {
    const { linkedin } = req.body;

    try{
        if(!linkedin){
            return res.status(404).json({
                status: false,
                message: 'Invalid Input'
            })
        }
        const response = await axios.post('https://www.signalhire.com/api/v1/candidate/search', {
            items: linkedin,
            callbackUrl: "https://app.klout.club/api/signalHireCallback"
        }, {
            headers: {
                'Content-Type': 'application/json',
                'apikey': '202.WAQInzOWWC8SE4V5j2pouI2lDwaL'
            }                                 
        });

        return res.status(200).json({
            status: true,
            data: response.data,
            message: 'Response'
        });
    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message
        })
    }
}