const { sendResponse, distanceBt2Points } = require("../../utils/utility");
const { FCMMessaging } = require("../../utils/firebase-notification");
const UserModel = require("../../models/user-model");
const UserChatModel = require("../../models/user-chat-model");
const moment = require("moment");
const ChatTrackView = require("../../models/chat-track-view");
const imageBaseUrl = process.env.DOWNLOAD_IMAGE_BASE_URL;
const mongoose = require('mongoose')

const chatUsersList = async (req, res) => {
  
  try {

    const id = req.headers["UserId"] || req.headers["userid"];

    if (!id) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "User Id is missing.",
      });
    }

    var list = await UserChatModel.aggregate([
      {
        $match: { fromUserId: id, isDeleted: 0 },
      },
      {
        $lookup: {
          let: { userObjId: { $toObjectId: "$toUserId" } },
          from: "users",
          pipeline: [{ $match: { $expr: { $eq: ["$_id", "$$userObjId"] } } }],
          as: "userDetails",
        },
      },
    ]).sort({ pinedStatus: -1, lastMessageDateTime: -1 });

    if (list) {
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "Chat users List.",
        data: {
          chatUsers: list,
          //connectionRequests: connReqList,
          //connectionsList
        },
      });
    }
  } catch (err) {
    console.log(err);
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: err,
    });
  }
};

const sendMessage = async (req, res) => {

  const { toUserId, message, title } = req.body;
  let connectionId = req.body.connectionId;

  const id = req.headers["UserId"] || req.headers["userid"];

  if(!connectionId){
    const existingChat = await UserChatModel.findOne({ fromUserId: id, toUserId: req.body.toUserId })
    if(existingChat){
      connectionId = existingChat.connectionId;
    }
  }

  var isNotify = true;

  if (!id) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "User Id is missing.",
    });
  }

  // Validate input
  if (!id || !toUserId || !message || !title) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "connectionId, toUserId, title and message is required!",
    });
  }

  const isExistData = await UserChatModel.findOne(

    { connectionId: connectionId, fromUserId: id, toUserId: req.body.toUserId },
    { __v: false }
  ).sort({ createdAt: -1 });

  if (isExistData) {
    const unreadCount = isExistData.unreadCount + 1;

    connectionId, id, toUserId;


    const result = await UserChatModel.updateOne(
      {
        connectionId: connectionId,
        fromUserId: id,
        toUserId: req.body.toUserId,
      },
      {
        $set: {
          lastMessage: message,
          lastMessageDateTime: moment().format(),
          isDeleted: 0,
        },
      }
    );

    const chatTrack = await ChatTrackView.findOne({
      from_user_id: id,
      to_user_id: toUserId,
    })

    if(chatTrack){
      chatTrack.ModifiedDate = new Date();
      await chatTrack.save();
    }

  } else {
    if(!connectionId){
      const premiumUser = await UserModel.findOne({_id: id}, {password: false});
      if(premiumUser.role === 'subadmin'){
        const objectId = new mongoose.Types.ObjectId()
        connectionId = objectId.toString();
        console.log(connectionId);
      }else{
        return await sendResponse(req, res, {
          successStatus: false,
          statusCode: 400,
          msg: 'Invalid User Id',
        });
      }
    }

    const data = new UserChatModel({
      connectionId: connectionId,
      fromUserId: id,
      toUserId: req.body.toUserId,
      lastMessage: req.body.message,
      lastMessageDateTime: moment().format(),
      unreadCount: 0,
      muteStatus: 0,
      pinedStatus: 0,
      isDeleted: 0,
    });
    try {
      const userChat = await data.save();

      //ChatTrack
      var ChatTrackViewData = {
        from_user_id: id,
        connectionId: connectionId,
        to_user_id: toUserId,
        chatStatus: 1,
        ModifiedDate: new Date(),
      };
      const ChatTrackViewInsert = new ChatTrackView(ChatTrackViewData);
      const ChatTrackViewInsertIns = await ChatTrackViewInsert.save();

    } catch (error) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: error.message,
        data: error,
      });
    }
  }
  const isExistData2 = await UserChatModel.findOne(
    { connectionId: connectionId, fromUserId: req.body.toUserId, toUserId: id },
    { __v: false }
  ).sort({ createdAt: -1 });
  if (isExistData2) {
    const unreadCount2 = isExistData2.unreadCount + 1;
    if (isExistData2.muteStatus == 1) {
      isNotify = false;
    }
    const result = await UserChatModel.updateOne(
      {
        connectionId: connectionId,
        fromUserId: req.body.toUserId,
        toUserId: id,
      },
      {
        $set: {
          unreadCount: unreadCount2,
          lastMessage: message,
          lastMessageDateTime: moment().format(),
          isDeleted: 0,
        },
      }
    );

  } else {
    const data1 = new UserChatModel({
      connectionId: connectionId,
      fromUserId: req.body.toUserId,
      toUserId: id,
      lastMessage: req.body.message,
      lastMessageDateTime: moment().format(),
      unreadCount: 1,
      muteStatus: 0,
      pinedStatus: 0,
      isDeleted: 0,
    });
    try {
      const userChat2 = await data1.save();

    } catch (error) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: error.message,
        data: error,
      });
    }
  }
  const userToData = await UserModel.findOne({ _id: req.body.toUserId });
  if (isNotify && userToData.deviceToken) {
    /** FCM Notification */
    const FCMMessage = {
      message: {
      //this may vary according to the message type (single recipient, multicast, topic, et cetera)
      token: userToData.deviceToken,
      notification: {
        title: req.body.title,
        body: req.body.message,
      },
      data: {
        Type: "4",
        Title: req.body.title,
        Body: req.body.message,
        ConnectionId: connectionId,
        RequesterId: id,
        Name: req.body.title,
      }},
    };
    FCMMessaging(FCMMessage);
  }
  /** FCM Notification */
  return await sendResponse(req, res, {
    successStatus: true,
    statusCode: 200,
    msg: "Message sent successfully",
    data: {},
  });
};

const updateChatUserStatus = async (req, res) => {
  try {

    const request = req.body;

    request.updatedAt = moment().format("YYYY-MM-DD HH:mm:ss");

    const id = req.headers["UserId"] || req.headers["userid"];

    // Validate user input
    if (!id) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 200,
        msg: "User Id is missing.",
      });
    }

    const result = await UserChatModel.updateOne(
      {
        connectionId: request.connectionId,
        fromUserId: id,
        toUserId: request.toUserId,
      },
      {
        $set: request,
      }
    );

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Data updated successfully!",
      data: {},
    });
  } catch (err) {
    console.log(err);
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: err,
    });
  }
};

module.exports = {
  chatUsersList,
  sendMessage,
  updateChatUserStatus,
};
