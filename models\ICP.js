
const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const ICPItemSchema = new Schema({
  id: {
    type: String,
  },
  value: {
    type: String,
    required: true,
  },
});

const ICPSchema = new Schema({
  user_id: {
    type: Schema.Types.ObjectId,
    required: true,
  },
  country_id: {
    type: String,
    required: true,
  },
  countries: [ICPItemSchema],
  companies: [ICPItemSchema],
  employeeSize: [ICPItemSchema],
  skills: [ICPItemSchema],
  industries: [ICPItemSchema],
  states: [ICPItemSchema],
  jobTitles: [ICPItemSchema]
});

module.exports = mongoose.model("ICP", ICPSchema);
