const express = require("express");
const http = require("http");
const { Server } = require("socket.io");
require("dotenv").config();
require("./controllers/v1/emailController");

const cors = require("cors");
const mongoose = require("mongoose");
const UnsubscribeModel = require("./models/unsubscribe");
const bodyParser = require("body-parser");

const fileUpload = require("express-fileupload");
const WhatsappMessageStatusModel = require("./models/whatsappMessageStatus");
const EmailMessageStatusModel = require("./models/emailMessageStatus");
const { reducePlanExpiryDays } = require('./controllers/v1/premium-user-controller');
const schedule = require("node-schedule");
const PeopleModel = require('./models/people');
const { removeWhiteSpaceFromNumber, trimToTenDigits } = require('./utils/utility');


schedule.scheduleJob("0 0 * * *", reducePlanExpiryDays);


const mongoString = process.env.DATABASE_URL;
const PORT = process.env.PORT;
const axios = require("axios");

mongoose.set("strictQuery", true);
mongoose.connect(mongoString, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const database = mongoose.connection;

global.__basedir = __dirname + "/..";

database.on("error", (error) => {
  console.log(error);
});

mongoose.set("debug", true);

database.once("connected", () => {
  console.log("Database Connected");
});

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
  },
  transports: ["websocket", "polling"],
});

app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(bodyParser.json({ type: "application/json" }));

// run the cronjob to reduce the planExpiryDays
reducePlanExpiryDays();


// Middleware to handle file uploads
app.use(fileUpload());

app.get("/", (req, res) => {
  res.status(200).json({ message: "Welcome to klout App !" });
});

app.get("/appVersion", (req, res) => {
  const version = "2.0.17";
  return res.status(200).json({
    status: true,
    data: version,
    message: "App Version",
  });
});

// this is the code for the websocket
io.on('connection', (socket) => {
  console.log('A user connected');

  socket.on('joinRoom', ({ userId, eventUuid, tabId }) => {
    const room = `${userId}:${eventUuid}:tab${tabId}`;
    socket.join(room);
    console.log(`User joined room: ${room}`);
  });

  // User joins a specific event room
  socket.on('joinEvent', ({ userId, eventUuid }) => {
    const room = `${userId}:${eventUuid}`;
    socket.join(room);
    console.log(`User with ID ${userId} joined event room: ${room}`);
  });

  socket.on('disconnect', () => {
    console.log('A user disconnected');
  });
});

app.post('/event-check-in/badge-print', async (req, res) => {
  const {
    imageUrl,
    attendeeName,
    attendeeRole,
    attendeeCompany,
    eventOwnerId,
    eventUuid,
    tabId
  } = req.body;

  try {
    const badgeData = {
      imageUrl,
      attendeeName,
      attendeeRole,
      attendeeCompany,
      eventOwnerId,
      eventUuid,
      tabId,
    };


    const room = `${eventOwnerId}:${eventUuid}:tab${tabId}`;
    io.to(room).emit('badgeGenerated', badgeData);
    console.log("Badge data emitted to room:", room);

    res.status(200).json({
      message: 'Check-in successful',
      badgeData,
    });
  } catch (error) {
    return res.status(400).json({
      error: error.message,
    });
  }
});

app.post('/webhook/check-in', (req, res) => {
  const { userId, currentCheckedInCount, eventUuid } = req.body;

  const room = `${userId}:${eventUuid}`;

  if (currentCheckedInCount === undefined || !eventUuid) {
    return res.status(400).json({ error: 'currentCheckedInCount and eventUuid are required' });
  }

  // Increment the check-in count
  const updatedCheckInCount = currentCheckedInCount + 1;

  // Store the updated count in memory for the event
  // checkInCounts[eventUuid] = updatedCheckInCount;

  // Broadcast the updated count to all connected clients in the event room
  io.to(room).emit('checkInCountUpdated', { eventUuid, updatedCheckInCount });
  console.log(`Check-in count for event ${eventUuid} updated to: ${updatedCheckInCount}`);

  // Return the updated count and event UUID
  res.status(200).json({
    message: 'Check-in count updated successfully',
    eventUuid,
    updatedCheckInCount,
  });
});

// Webhooks and other routes remain unchanged
app.post("/webhook/whatsapp-message-status", async (req, res) => {
  const statusUpdate = req.body;
  const timestamp = statusUpdate.timestamp;
  const customerID = statusUpdate.data.customer.id;
  const customerPhoneNumber = statusUpdate.data.customer.phone_number;
  const messageID = statusUpdate.data.message.id;
  const messageStatus = statusUpdate.data.message.message_status;

  try {
    await WhatsappMessageStatusModel.updateOne(
      { messageID },
      {
        $set: {
          messageID,
          messageStatus,
          customerPhoneNumber,
          timestamp: new Date(timestamp),
        },
      },
      { upsert: true }
    );
    console.log("Status update saved to database");
  } catch (error) {
    console.error("Error saving status update:", error);
  }

  console.log(req.body);

  res.status(200).send("Status update received");
});

app.post("/webhook/ses-email-status", async (req, res) => {
  console.log("Received raw body:", req.rawBody);

  try {
    const { Type, Message } = req.body;

    if (Type === "Notification") {
      let parsedMessage;
      try {
        parsedMessage = JSON.parse(Message);
        const timestamp = parsedMessage.mail.timestamp;
        const customerEmail = parsedMessage.mail.destination; // select [0]
        const messageID = parsedMessage.mail.messageId;
        const messageStatus = parsedMessage.eventType;
        const headers = parsedMessage.mail.headers;
        const customHeader = headers.find((header) => header.name === "X-CUSTOM-MESSAGE-ID");
        const customMessageID = customHeader ? customHeader.value : null;

        await EmailMessageStatusModel.updateOne(
          { messageID: customMessageID },
          {
            $set: {
              messageID: customMessageID,
              messageStatus,
              customerEmail: customerEmail[0],
              timestamp,
            },
          },
          { upsert: true }
        );
        console.log("Parsed Notification Message:", parsedMessage);
      } catch (err) {
        console.error("Failed to parse message:", err.message);
      }
    }

    res.status(200).send("OK"); // Acknowledge receipt of the message
  } catch (error) {
    console.error("Error handling SNS message:", error.message);
    res.status(500).send("Error");
  }
});


// app.post("/api/signalHireCallback", async (req, res) => {
//   try {
//     const callbackData = req.body; // Extract JSON payload

//     // Pretty print the full JSON response
//     console.log("Received callback data:", JSON.stringify(callbackData, null, 2));

//     // const contacts = callbackData;
//     // console.log(contacts)

//     for (let data of callbackData) {

//       // console.log(contact)

//       const people = await PeopleModel.findOne({ linkedinUrl: data.item });

//       const eachContact = data.candidate.contacts;
//       // console.log(eachContact);

//       // // Separate phones and emails
//       const emails = eachContact.filter(c => c.type === 'email');
//       const phones = eachContact.filter(c => c.type === 'phone');

//       // Function to select preferred contact
//       function selectBestContact(arr, preferredSubType) {
//         if (arr.length === 1) return arr[0].value;

//         const bestMatch = arr.find(c => c.subType === preferredSubType && c.rating === "100");
//         if (bestMatch) return bestMatch.value;

//         return arr[0].value; // fallback: return the first one
//       }

//       // Select email
//       const selectedEmail = selectBestContact(emails, 'personal');

//       // Select phone
//       const selectedPhone = selectBestContact(phones, 'mobile');

//       // console.log("Selected Email:", selectedEmail);
//       // console.log("Selected Phone:", selectedPhone);


//       const existingNumber = people.mobileNumber;
//         if(!existingNumber){
//           people.mobileNumber = Number(trimToTenDigits(removeWhiteSpaceFromNumber(selectedPhone)));
//         }

//         const existingEmail = people.email;
//         if(!existingEmail){
//           people.email = selectedEmail;
//         }

//         await people.save();

//         await axios.post('https://api.klout.club/api/phone-email-update', { linkedin_url:data.item, email: selectedEmail, phone_number: trimToTenDigits(removeWhiteSpaceFromNumber(selectedPhone)) }, {
//           headers: {
//             'Content-Type': 'application/json'
//           }
//         });

//     }

//     res.status(200).send({ message: "Callback received successfully" });
//   } catch (error) {
//     console.error("Error processing callback:", error);
//     res.status(500).send({ message: "Internal Server Error" });
//   }
// });



app.post("/api/signalHireCallback", async (req, res) => {
  try {
    const callbackData = req.body;

    console.log("Received callback data:", JSON.stringify(callbackData, null, 2));

    for (let data of callbackData) {
      const people = await PeopleModel.findOne({ linkedinUrl: data.item });
      // if (!people) continue; // skip if no match

      const eachContact = data.candidate.contacts || [];

      const emails = eachContact.filter(c => c.type === 'email');
      const phones = eachContact.filter(c => c.type === 'phone');

      // Select preferred contact if available
      function selectBestContact(arr, preferredSubType) {
        if (!Array.isArray(arr) || arr.length === 0) return null;

        if (arr.length === 1) return arr[0].value;

        const bestMatch = arr.find(c => c.subType === preferredSubType && c.rating === "100");
        return bestMatch ? bestMatch.value : arr[0].value;
      }

      // Safely define selectedEmail and selectedPhone
      const selectedEmail = selectBestContact(emails, 'personal');
      const selectedPhone = selectBestContact(phones, 'mobile');

      // Update only if they exist and are missing in the DB
      if(people){

        if (selectedPhone && !people.mobileNumber) {
          people.mobileNumber = Number(trimToTenDigits(removeWhiteSpaceFromNumber(selectedPhone)));
        }
  
        if (selectedEmail && !people.email) {
          people.email = selectedEmail;
        }
  
        await people.save();

      }

      console.log('hello')
      

      // Send webhook only if at least one value is present
      await axios.post('https://api.klout.club/api/phone-email-update', {
        linkedin_url: data.item,
        email: selectedEmail || "",
        phone_number: selectedPhone ? trimToTenDigits(removeWhiteSpaceFromNumber(selectedPhone)) : ""
      }, {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    res.status(200).send({ message: "Callback received successfully" });
  } catch (error) {
    console.error("Error processing callback:", error);
    res.status(500).send({ message: "Internal Server Error" });
  }
});



app.post("/api/apolloCallback", async (req, res) => {
  try {
    const callbackData = req.body; // Extract JSON payload

    // Pretty print the full JSON response
    console.log("Received callback data:", JSON.stringify(callbackData, null, 2));

    res.status(200).send({ message: "Callback received successfully" });
  } catch (error) {
    console.error("Error processing callback:", error);
    res.status(500).send({ message: "Internal Server Error" });
  }
});



app.get("/api/unsubscribe/:email", async (req, res) => {
  try {
    const { email } = req.params;

    const existingEntry = await UnsubscribeModel.findOne({ email });
    if (existingEntry) {
      return res.status(200).json({ message: "Email is already unsubscribed." });
    }

    const unsubscribeMember = new UnsubscribeModel({ email });
    await unsubscribeMember.save();

    res.send("You have been successfully unsubscribed.");
  } catch (error) {
    console.error("Error unsubscribing email:", error);
    res.status(500).json({ error: "Internal server error." });
  }
});

app.use("/api/v1", require("./routes/v1"));

app.use("/api/v2", require("./routes/v1"));

app.use("/api/marketing", require("./routes/marketing/v1"));

app.use("/api/mapping/v1", require("./routes/mapping/v1"));

app.use("/api/organiser/v1", require("./routes/organiser/v1"));
// app.use("/api/tls/v1", require('./routes/v1/tls-route'));


try {
  server.listen(PORT, () => {
    console.log(`Server Started at ${PORT}`);
  });
  server.setTimeout(5 * 60 * 1000);
} catch (error) {
  console.error(`Error starting the server: ${error.message}`);
}
