const mongoose = require('mongoose');

const industrySchema = new mongoose.Schema({
    industryName: {
        required: true,
        type: String
    },
    status: {
        default: 1,
        type: Number
    }
},{
    timestamps: {
      createdAt: 'createdAt', // Use `created_at` to store the created date
      updatedAt: 'updatedAt' // and `updated_at` to store the last updated date
    }
})

module.exports = mongoose.model('industry', industrySchema)