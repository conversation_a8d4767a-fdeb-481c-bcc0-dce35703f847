const { sendResponse } = require("../../utils/utility");
const CityModel = require("../../models/city-model");

const create = async (req, res) => {

  const { city, isPrimary, isPopular } = req.body;

  // Validate user input
  if (!city) {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "city name is required!",
      data: exist,
    });
  }
  

  const exist = await CityModel.findOne({ city });

  if (exist) {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "City Name Already Exist, Please try with other name",
      data: exist,
    });
  }

  const data = new CityModel({
    city: city,
    isPopular: isPopular,
    isPrimary: isPrimary,
  });

  try {
    const cityData = await data.save();
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "City add successfully!",
      data: cityData,
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: error.message,
      data: error,
    });
  }
};

const getCitiesByStateId = async (req, res) => {
  try {
    const { state_id  } = req.body;

    const Cities = await CityModel.find({ state_id: state_id });

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "All Cities",
      data: Cities,
    });
  } catch (error) {
   return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
      data: error,
    });
  }
};

const getCitiesByStateCountryId = async (req, res) => {
  try {
    const { country_id, state_id  } = req.body;

    const Cities = await CityModel.find({ country_id: country_id,  state_id: state_id });

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "All Cities",
      data: Cities,
    });
  } catch (error) {
   return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
      data: error,
    });
  }
};

const getAll = async (req, res) => {

  try {

    const popular = await CityModel.find(
      { status: 1, isPopular: 1 },
      { __v: false }
    );

    const others = await CityModel.find(
      { status: 1, isPopular: 0 },
      { __v: false }
    );

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "City list retrieved successfully",
     data: { popular, others },
    });
    
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
      data: error,
    });
  }
};

const search = async (req, res) => {
  try {
    let query = {};
   
    const string = req?.body?.string;
   
    if (string) {
      query.skill = { $regex: string, $options: "i" };
    } else {

      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "City list retrieved successfully.",
        data: {},
      });
    }

    query.status = 1;

    const data = await searchData(query);

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "City list retrieved successfully.",
      data: data,
    });

  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
      data: error,
    });
  }
};

const details = async (req, res) => {
  try {

    const { id } = req.body;

    if (!id) {
      return await sendResponse(req, res, {
        statusCode: 400,
        msg: "City id is required!",
      });
    }

    const data = await CityModel.findOne({ _id: id }, { __v: false });

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Details  retrieved successfully",
      data: data,
    });

  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
      data: error,
    });
  }
};

const searchData = async (query) => {
  return await CityModel.find(query, { __v: false });
};

module.exports = {
  create,
  getAll,
  search,
  details,
  getCitiesByStateId,
  getCitiesByStateCountryId
};
