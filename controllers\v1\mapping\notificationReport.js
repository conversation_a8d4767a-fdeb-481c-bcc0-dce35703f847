const FlexApiBase = require('twilio/lib/rest/FlexApiBase');
const NotificationModel = require('../../../models/notifications');
const UserModel = require('../../../models/user-model');

exports.test = async (req, res) => {
    try{
        return res.status(200).json({
            message: 'notification report is working fine'
        })
    }catch(error){
        return res.status(404).json({
            message: 'Something went wrong'
        })
    }
}

exports.sendConnectionRequestReport = async (req, res) => {
    try {
        const allConnectionRequest = await NotificationModel.find({ type: 1 })
            .sort({ createdAt: -1 })
            .populate({
                path: 'user_id', 
                select: 'first_name last_name',  
                model: UserModel,  
            });

        // If user is not present, set the user name to 'unknown'
        const modifiedRequests = allConnectionRequest.map(request => {
            if (!request.user_id || !request.user_id.first_name) {
                request.user_id = { first_name: 'Unknown', last_name: 'Unknown' };
            }
            return request;
        });

        return res.status(200).json({
            status: true,
            data: modifiedRequests,
            message: 'All Connection Request Report'
        });
    } catch (error) {
        return res.status(400).json({
            status: false,
            message: error.message
        });
    }
};


// this controller to get the report related to type 2 which is send after accepting the request
exports.acceptConnectionReport = async (req, res) => {
    try{
        const allAcceptConnection = await NotificationModel.find({type: 2}).sort({createdAt: -1})
        .populate({
            path: 'user_id', 
            select: 'first_name last_name', 
            model: UserModel,  
        });

        const modifiedRequests = allAcceptConnection.map(request => {
            if (!request.user_id || !request.user_id.first_name) {
                request.user_id = { first_name: 'Unknown', last_name: 'Unknown' };
            }
            return request;
        });


        return res.status(200).json({
            status: true,
            data: modifiedRequests,
            message: 'All Accept Request Report '
        })
    }catch(error){
        return res.status(400).json({
            status: false,
            message: error.message
        })
    }
}


// this controller to get the report related to type 2 which is send after accepting the request
exports.profileViewReport = async (req, res) => {
    try{
        const allProfileView = await NotificationModel.find({type: 3}).sort({createdAt: -1})
        .populate({
            path: 'user_id', 
            select: 'first_name last_name', 
            model: UserModel,  
        });

        const modifiedRequests = allProfileView.map(request => {
            if (!request.user_id || !request.user_id.first_name) {
                request.user_id = { first_name: 'Unknown', last_name: 'Unknown' };
            }
            return request;
        });

        return res.status(200).json({
            status: true,
            data: modifiedRequests,
            message: 'All Profile View Report '
        })
    }catch(error){
        return res.status(400).json({
            status: false,
            message: error.message
        })
    }
}