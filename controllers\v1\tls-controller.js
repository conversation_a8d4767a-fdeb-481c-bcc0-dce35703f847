const AgendaRatingModel = require('../../models/agenda-rating');
const AttendeeCheckedInModel = require('../../models/attendee-checked-in');
const UserModel = require('../../models/user-model');
const CaptureUserOnTls = require('../../models/capture-user-on-tls');
const xml2js = require('xml2js');
const axios = require('axios');
const NewsPublisherModel = require('../../models/news-publisher');
const companyMasterModel = require('../../models/companyMaster');

function calculatescoreOnLinkedinFollowers(followers){
    let linkedinFollowerScore = 0;

    switch (true) {
        case followers >= 0 && followers <= 500:
            linkedinFollowerScore = 5;
            break;
        case followers >= 501 && followers <= 1000:
            linkedinFollowerScore = 10;
            break;
        case followers >= 1001 && followers <= 2000:
            linkedinFollowerScore = 15;
            break;
        case followers >= 2001 && followers <= 3500:
            linkedinFollowerScore = 20;
            break;
        case followers >= 3501 && followers <= 5000:
            linkedinFollowerScore = 25;
            break;
        case followers >= 5001 && followers <= 7000:
            linkedinFollowerScore = 30;
            break;
        case followers >= 7001 && followers <= 10000:
            linkedinFollowerScore = 35;
            break;
        case followers >= 10001 && followers <= 15000:
            linkedinFollowerScore = 40;
            break;
        case followers >= 15001 && followers <= 25000:
            linkedinFollowerScore = 45;
            break;
        case followers >= 25001 && followers <= 50000:
            linkedinFollowerScore = 50;
            break;
        case followers >= 50001 && followers <= 150000:
            linkedinFollowerScore = 60;
            break;
        case followers >= 150001 && followers <= 250000:
            linkedinFollowerScore = 65;
            break;
        default:
            linkedinFollowerScore = 75;
            break;
        }
        

    return linkedinFollowerScore;
}

function calculatescoreOnXFollowers(followers){
    let xFollowerScore = 0;

    switch (true) {
        case followers >= 0 && followers <= 500:
            xFollowerScore = 5;
            break;
        case followers >= 501 && followers <= 1000:
            xFollowerScore = 10;
            break;
        case followers >= 1001 && followers <= 2000:
            xFollowerScore = 15;
            break;
        case followers >= 2001 && followers <= 3500:
            xFollowerScore = 20;
            break;
        case followers >= 3501 && followers <= 5000:
            xFollowerScore = 25;
            break;
        case followers >= 5001 && followers <= 7000:
            xFollowerScore = 30;
            break;
        case followers >= 7001 && followers <= 10000:
            xFollowerScore = 35;
            break;
        case followers >= 10001 && followers <= 15000:
            xFollowerScore = 40;
            break;
        case followers >= 15001 && followers <= 25000:
            xFollowerScore = 45;
            break;
        case followers >= 25001 && followers <= 50000:
            xFollowerScore = 50;
            break;
        case followers >= 50001 && followers <= 150000:
            xFollowerScore = 60;
            break;
        case followers >= 150001 && followers <= 250000:
            xFollowerScore = 65;
            break;
        default:
            xFollowerScore = 75;
            break;
        }
        

    return xFollowerScore;
}

function calculateScoreRegardingToDaAndVisits(da, visits){
    const websiteDa = Number(da);
    const websiteVisits = Number(visits);
    let daScore = 0;
    let visitsScore = 0;

    // DA Score Calculation
    switch (true) {
    case websiteDa >= -1 && websiteDa <= 10:
        daScore = 1;
        break;
    case websiteDa >= 11 && websiteDa <= 20:
        daScore = 2;
        break;
    case websiteDa >= 21 && websiteDa <= 30:
        daScore = 3;
        break;
    case websiteDa >= 31 && websiteDa <= 40:
        daScore = 4;
        break;
    case websiteDa >= 41 && websiteDa <= 50:
        daScore = 5;
        break;
    case websiteDa >= 51 && websiteDa <= 60:
        daScore = 6;
        break;
    case websiteDa >= 61 && websiteDa <= 70:
        daScore = 7;
        break;
    case websiteDa >= 71 && websiteDa <= 80:
        daScore = 8;
        break;
    case websiteDa >= 81 && websiteDa <= 90:
        daScore = 9;
        break;
    case websiteDa >= 91 && websiteDa <= 100:
        daScore = 10;
        break;
    default:
        daScore = 1; // Invalid DA value
        break;
    }

    // Visits Score Calculation
    switch (true) {
    case websiteVisits >= -1 && websiteVisits <= 1000000: // 1M
        visitsScore = 1;
        break;
    case websiteVisits > 1000000 && websiteVisits <= 3000000: // 1M - 3M
        visitsScore = 2;
        break;
    case websiteVisits > 3000000 && websiteVisits <= 5000000: // 3M - 5M
        visitsScore = 3;
        break;
    case websiteVisits > 5000000 && websiteVisits <= 10000000: // 5M - 10M
        visitsScore = 5;
        break;
    case websiteVisits > 10000000 && websiteVisits <= 25000000: // 10M - 25M
        visitsScore = 7;
        break;
    case websiteVisits > 25000000 && websiteVisits <= 50000000: // 25M - 50M
        visitsScore = 10;
        break;
    case websiteVisits > 50000000 && websiteVisits <= *********: // 50M - 100M
        visitsScore = 12;
        break;
    case websiteVisits > ********* && websiteVisits <= *********: // 100M - 250M
        visitsScore = 15;
        break;
    case websiteVisits > ********* && websiteVisits <= *********: // 250M - 500M
        visitsScore = 18;
        break;
    default:
        visitsScore = 1; // Invalid visits value
        break;
    }

    return daScore + visitsScore;

}

// Extract and calculate score for company size
function calculateScoreRegardingCompanySize(employeeSize) {
    const sizeRange = extractSizeRange(employeeSize);

    const midRangeValue = (sizeRange.min + sizeRange.max)/2;
    let companyScore = 0;
  
    switch (true) {
      case midRangeValue === 0 && midRangeValue <= 50:
        companyScore = 3;
        break;
      case midRangeValue >= 51 && midRangeValue <= 200:
        companyScore = 5;
        break;
      case midRangeValue >= 201 && midRangeValue <= 500:
        companyScore = 7;
        break;
      case midRangeValue >= 501 && midRangeValue <= 1000:
        companyScore = 10;
        break;
      case midRangeValue >= 1001 && midRangeValue <= 2000:
        companyScore = 13;
        break;
      case midRangeValue >= 2001 && midRangeValue <= 5000:
        companyScore = 15;
        break;
      case midRangeValue >= 5001 && midRangeValue <= 10000:
        companyScore = 20;
        break;
      case midRangeValue >= 10001 && midRangeValue <= 50000:
        companyScore = 25;
        break;
      case midRangeValue >= 50001:
        companyScore = 30;
        break;
      default:
        companyScore = 0; // Return 0 if size is invalid
    }
  
    return companyScore;
}

function extractSizeRange(employeeSize) {
    // Remove any non-numeric characters except digits, commas, plus signs, and hyphens
    employeeSize = employeeSize.replace(/[^\d,+-]/g, "").trim();

    // If number has a '+' at the end, remove it
    if (employeeSize.endsWith("+")) {
        employeeSize = employeeSize.slice(0, -1);
    }

    // If the number has commas, remove them and convert it to an array if needed
    if (employeeSize.includes(",")) {
        employeeSize = employeeSize.replace(/,/g, "");
    }

    // Handle range case like "1000-5000"
    if (employeeSize.includes("-")) {
        let [min, max] = employeeSize.split("-").map(num => parseInt(num, 10));
        return { min, max };
    }

    // If only a single number remains, set it as min and max
    let num = parseInt(employeeSize, 10);
    return { min: num, max: num };
}

const designationRatings = {
    "entry-level & associates": 5,
    "junior-management": 8,
    "mid-management": 10,
    "senior-management": 15,
    "executive-leadership": 22,
    "c-level": 25,
};

function classifyDesignation(jobTitle) {
    // Define possible categories with keywords
    const designationMap = {
        "entry-level & associates": ["intern", "trainee", "analyst", "assistant", "coordinator", "executive assistant", "associate", "executive", "junior engineer", "coder"],
        "junior-management": ["team lead", "supervisor", "senior analyst", "specialist", "developer", "junior software engineer", "developer 1", "associate software engineer", "it support engineer", "systems administrator", "cloud engineer", "coordinator", "content writer", "representative", "pharmacist", "technician"],
        "mid-management": ["manager", "senior manager", "associate director", "head", "business partner", "chief manager"],
        "senior-management": ["director", "general manager", "deputy general manager", "finance controller", "avp", "assistant vice president"],
        "executive-leadership": ["vp", "vice president", "svp", "senior vice president", "evp", "executive vice president", "president"],
        "c-level": ["chief executive officer", "chief financial officer", "chief technology officer", "managing director", "chief operating officer", "chief marketing officer", "chief human resource officer", "chief information officer", "chief product officer", "ciso", "chief revenue officer", "chief customer officer", "chief digital officer", "chief strategy officer", "chief data officer", "chief innovation officer", "chief investment officer", "chief information security officer", "chief technology & product officer", "chief technology and product officer"],
    };
    
    // Additional keywords for partial matching
    const partialKeywords = {
        "entry-level & associates": ["intern", "trainee", "analyst", "assistant", "coordinator", "executive assistant", "associate", "executive", "junior engineer", "coder"],
        "junior-management": ["team lead", "supervisor", "senior analyst", "specialist", "developer", "junior software engineer", "developer 1", "associate software engineer", "it support engineer", "systems administrator", "cloud engineer", "coordinator", "content writer", "representative", "pharmacist", "technician"],
        "mid-management": ["head", "lead", "manager"],
        "senior-management": ["director", "general"],
        "executive-leadership": ["vice", "president"],
        "c-level": ["chief", "cfo", "ceo", "cto", "coo", "cmo"]
    };
    
    // Normalize job title
    const normalizedTitle = jobTitle.toLowerCase();
    let classification = "unknown";
    
    // Check for exact match first
    for (const category in designationMap) {
        if (designationMap[category].some(keyword => normalizedTitle === keyword)) {
            classification = category;
            break;
        }
    }
    
    // Check for partial match if no exact match found
    if (classification === "unknown") {
        for (const category in partialKeywords) {
            if (partialKeywords[category].some(keyword => normalizedTitle.includes(keyword))) {
                classification = category;
                break;
            }
        }
    }
    
    // Adjust score if "senior" is found in the title
    // let scoreAdjustment = normalizedTitle.includes("senior") ? 2 : 0;
    let scoreAdjustment = /senior|sr\.|sr\s/i.test(normalizedTitle) ? 2 : 0;

    
    return { classification, scoreAdjustment };
}

function getDesignationRating(jobTitle) {
    // Step 1: Classify the designation
    const { classification, scoreAdjustment } = classifyDesignation(jobTitle);

    // Step 2: Get the rating from the designationRatings map
    let rating = designationRatings[classification] || 0; // Default to 0 if unrecognized

    // Step 3: Apply score adjustment
    return rating + scoreAdjustment;
}


// this code is for give the agenda rating
exports.createAgendaRating = async (req, res) => {
    const { eventUuid, eventTitle, agendaTitle, agendaUuid, givenBy, givenTo, rating } = req.body;

    try {
        
        if(!eventUuid || !eventTitle || !agendaTitle || !agendaUuid || !givenBy || !givenTo){
            return res.status(404).json({
                status: false,
                message: 'All Fields are required'
            })
        }

        const existingRating = await AgendaRatingModel.findOne({ eventUuid, agendaUuid, givenBy, givenTo });

        if(existingRating){
            existingRating.eventUuid = eventUuid;
            existingRating.eventTitle = eventTitle;
            existingRating.agendaTitle = agendaTitle;
            existingRating.agendaUuid = agendaUuid;
            existingRating.givenBy = givenBy;
            existingRating.givenTo = givenTo;
            existingRating.rating = rating;
            
            await existingRating.save();

            return res.status(200).json({
                status: true,
                data: existingRating,
                message: 'Agenda Rating is udpated successfully'
            });
        }

        const newRating = new AgendaRatingModel({ eventUuid, eventTitle, agendaTitle, agendaUuid, givenBy, givenTo, rating });

        await newRating.save();

        return res.status(201).json({
            status: true,
            data: newRating,
            message: 'Agenda Rating is added successfully'
        });
    } catch (error) {
        return res.status(500).json({
            status: false,
            message: error.message
        })
    }
};

// this code is for view the agenda reting
exports.viewAgendaRating = async (req, res) => {
    const { eventUuid, agendaUuid, givenBy  } = req.body;
    try{

        if(!eventUuid || !agendaUuid || !givenBy){
            return res.status(404).json({
                status: false,
                message: 'Invalid Input'
            })
        }

        const agendaRating = await AgendaRatingModel.find({ eventUuid, agendaUuid, givenBy });

        if(agendaRating.length < 1){
            return res.status(200).json({
                status: false,
                message: 'No agenda rating found'
            });
        }

        return res.status(200).json({
            status: true,
            data: agendaRating,
            rating: agendaRating[0].rating,
            message: 'Agenda Ratings'
        });

    }catch(error){

        return res.status(500).json({
            status: false,
            message: error.message
        });

    }
}




// this code is for calculate the thought leadership 
exports.thoughtLeadershipScore = async (req, res) => {
    const { mobileNumber } = req.body;
    let score = 0;
    try{

        if(!mobileNumber){
            return res.status(404).json({
                status: false,
                message: 'Please provide mobile number'
            });
        }

        const allAttendedEvents = await AttendeeCheckedInModel.find({ mobileNumber });
        const allAgendaRating = await AgendaRatingModel.find({ givenTo: mobileNumber });

        // score got update as per the role which is the user attend the event
        allAttendedEvents.forEach(eachAttendedEvent => {
            if(eachAttendedEvent.status.toLocaleLowerCase() == 'delegate'){
                score = score + 1;
            }else if(eachAttendedEvent.status.toLocaleLowerCase() == 'speaker'){
                score = score + 5;
            }else if(eachAttendedEvent.status.toLocaleLowerCase() == 'sponsor'){
                score = score + 2;
            }else if(eachAttendedEvent.status.toLocaleLowerCase() == 'penalist'){
                score = score + 5;
            }else if(eachAttendedEvent.status.toLocaleLowerCase() == 'moderator'){
                score = score + 4;
            }else{
                score = score + 1;
            }

            if(eachAttendedEvent.awardWinner){
                score = score + 7;
            }
        });

        // score update as per user by agenda rating given by checked in users
        allAgendaRating.forEach(eachAgenda => {
            score = score + eachAgenda.rating
        });

        // check if the user is premium or not
        const user = await UserModel.findOne({ mobileNumber });

        if(user.role == 'premium'){
            score = score + 2;
        }

        if(user.linkedinProfileUrl && user.linkedinProfileUrl != ''){
            score = score + 2;
            if(user.linkedinFollowers){
                score = score + calculatescoreOnLinkedinFollowers(user.linkedinFollowers)
            }
        }

        if(user.xProfileUrl && user.xProfileUrl != ''){
            score = score + 1;
            if(user.xFollowers){
                score = score + calculatescoreOnXFollowers(user.xFollowers);
            }
        }

        // rss news query will execute here

        if(user.company.toLowerCase() !== 'others'){
            // Construct the Google News RSS search URL
            const query = `"${user.first_name} ${user.last_name}" "${user.company}" "${user.designation}"`;
            const rssUrl = `https://news.google.com/rss/search?q=${encodeURIComponent(query)}&hl=en-IN&gl=IN&ceid=IN:en`;

            
            // Fetch RSS feed
            const headers = {
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
                Accept: 'application/rss+xml',
                'Accept-Language': 'en-US,en;q=0.9',
            };
            const response = await axios.get(rssUrl, { headers });
            const xmlData = response.data;
            
            // Parse XML response
            const parser = new xml2js.Parser();
            const result = await parser.parseStringPromise(xmlData);

             // Ensure `items` is an array and contains elements
            const news = result?.rss?.channel?.[0]?.item || [];
            const hasNews = Array.isArray(news) && news.length > 0;

            if(hasNews){
                const source = [];
                for(let eachNews of news){
                    const sourceItem = {
                        source: eachNews.source[0]._,
                        websiteUrl : eachNews.source[0].$.url,
                        publishingDate: eachNews.pubDate[0]
                    }
                    source.push(sourceItem)
                }
                console.log(source)


                for(let item of source){
                    const existing = await NewsPublisherModel.findOne({ url: item.websiteUrl });
                    if(!existing){
                        const newEntry = new NewsPublisherModel({ name: item.source, url: item.websiteUrl, da: -1, visits: "-1" });
                        newEntry.save();
                        score = score + 1; //  score for da
                        score = score + 1; // score for visits
                    }else{
                        const da = existing.da;
                        const visits = existing.visits;

                        score = score + calculateScoreRegardingToDaAndVisits(da, visits);
                    }
                }
            }

        }

        // score based on company size
        if(user.company && user.company.toLowerCase() !== 'others'){
            const searchCompany = await companyMasterModel.findOne({ company: user.company.toLowerCase() });

            if(searchCompany){
                const employeeSize = searchCompany.companySize;
                score = score + calculateScoreRegardingCompanySize(employeeSize);
            }
        }

        // score based on designation
        if(user.designation && user.designation.toLowerCase() !== 'others'){
            const designation = user.designation.toLowerCase();
            score = score + getDesignationRating(designation);
        }

    

        return res.status(200).json({
            status: true,
            score: score,
            message: 'Thought leadership score'
        })

    }catch(error){
        return res.status(500).json({
            status: false,
            message: error.message
        })
    }
}

//this code is to capture the users who clicks on the tls button how tls is calculated
exports.captureUserOnTlsButton = async (req, res) => {
    const { userId } = req.body;
    try{

        if(!userId){
            return res.status(200).json({
                status: false,
                message: 'userid is required'
            });
        }

        const isValidUser = await UserModel.findById(userId);

        if(!isValidUser){
            return res.status(200).json({
                status: false,
                message: 'Invalid User'
            })
        }

        const existing = await CaptureUserOnTls.findOne({ userId });

        if(existing){
            existing.clickCount = existing.clickCount + 1;
            await existing.save();

            return res.status(200).json({
                status: true,
                message: 'Update successfully'
            });
        }

        const newEntry = new CaptureUserOnTls({ userId, clickCount: 1 });
        await newEntry.save();

        return res.status(201).json({
            status: true,
            message: 'Created successfully'
        })

    }catch(error){

        return res.status(500).json({
            status: false,
            message: error.message
        })

    }
}

// this code is to list all the user which click on the tls button
exports.listAllUserClickOnTls = async (req, res) => {
    try {

    const allData = await CaptureUserOnTls.find({});
    const list = []

    for(let data of allData){
        const isUser = await UserModel.findById(data.userId, { first_name: true, last_name: true, _id: false });
        const addedData = { ...isUser.toObject(), clickCount: data.clickCount }

        if(isUser){
            list.push(addedData)
        }
    }

    return res.status(200).json({
        status: true,
        data: list,
        message: 'All list regarding to click on tls'
    });

    }catch(error){
        return res.status(500).json({
            status: false,
            message: error.message
        })
    }
}


// this code is to perform the crud operation regarding to the news-publisher model

exports.getAllNewsPublisher = async (req, res) => {
    try{
        const allData = await NewsPublisherModel.find();

        return res.status(200).json({
            status: true,
            data: allData,
            message: 'List of all publishers'
        });

    }catch(error){

        return res.status(500).json({
            status: false,
            message: error.message
        })
    }
}

exports.addNewsPublisher = async (req, res) => {
    const { name, url, da, visits } = req.body;

    try{
        if(!name || !url){
            return res.status(404).json({
                status: false,
                message: 'Please provide name and url'
            });
        }
        const existing = await NewsPublisherModel.findOne({ url });

        console.log(existing)
        if(existing){
            return res.status(200).json({
                status: true,
                message: 'Record already exists'
            })
        }

        const newEntry = new NewsPublisherModel({ name, url, da, visits });
        await newEntry.save();

        return res.status(201).json({
            status: true,
            data: newEntry,
            message: 'New Publisher added successfully'
        })
    }catch(error){

        return res.status(500).json({
            status: false,
            message: error.message
        })

    }
}

exports.updateNewsPublisher = async (req, res) => {
    const { id } = req.params
    const updatedData = req.body;
    try{
        const updatedEntry = await NewsPublisherModel.findByIdAndUpdate(id,  updatedData, {
            new: true
        });

        if(!updatedEntry){
            return res.status(404).json({
                status: false,
                message: 'Something went wrong'
            })
        }

        return res.status(200).json({
            status: true,
            data: updatedEntry,
            message: 'Data updated successfully'
        });
    }catch(error){
        return res.status(500).json({
            status: false,
            message: error.message
        })
    }
}

exports.viewNewsPublisher = async (req, res) => {
    const { id } = req.params;
    try{
        const data = await NewsPublisherModel.findById(id);
        if(!data){
            return res.status(404).json({
                status: false,
                message: 'Something went wrong'
            });
        }

        return res.status(200).json({
            status: true,
            data,
            message: 'view news publisher'
        });
    }catch(error){

        return res.status(500).json({
            status: false,
            message: error.message
        });
    }
}

exports.deleteNewsPublisher = async (req, res) => {
    const { id } = req.params;

    try{
        const entry = await NewsPublisherModel.findByIdAndDelete(id);

        if(!entry){
            return res.status(404).json({
                status: false,
                message: 'Something went wrong'
            });
        }

        return res.status(200).json({
            status: true,
            message: 'Entry deleted successfully'
        });
    }catch(error){

        return res.status(500).json({
            status: false,
            message: error.message
        })
    }
}


