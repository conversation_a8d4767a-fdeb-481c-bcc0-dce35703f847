const express = require('express');
const router = express.Router();
const StateController = require('../../controllers/v1/state-controller');
// const auth = require("../../middleware/auth");

//Get all Method
router.get('/getAll', async (req, res) => {
    const exist = await StateController.getAll(req, res);
});

router.get('/:countryId', async (req, res) => {
    const exist = await StateController.getStatesByCountryId(req, res);
});

//Post Method
// router.post('/add', async (req, res) => {
//     const exist = await StateController.create(req, res);
// })

//find one  Method
// router.get('/findone', async (req, res) => { 
//     const exist = await StateController.details(req, res);
// })

// Search 
// router.get('/search', async (req, res) => { 
//     const exist = await StateController.search(req, res);
// })

module.exports = router;