const mongoose = require('mongoose');

const checkInSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    email: {
        type: String,
        required: true
    },
    mobile: {
        type: Number,
        required: true,
    },
    designation: {
        type: String,
        required: true
    },
    company: {
        type: String,
        required: true
    },
    eventName: {
        type: String,
        required: true
    },
    tenMinuteInvitationSent: {
        type: Boolean,
        default: false
    },
    oneDayInvitationSent: {
        type: Boolean,
        default: false
    },
    threeDayInvitationSent: {
        type: Boolean,
        default: false
    }
}, { timestamps: true })

module.exports = mongoose.model("check_in", checkInSchema);
