const mongoose = require('mongoose');
const { Schema } = mongoose;

const connectionRequestsSchema = new mongoose.Schema({
    user_id: {
        type: String,
        required: true,
    },
    from_user_id: {
        type: String,
        required: true,
    },
    title: {
        type: String,
        required: true,
    },
    body: {
        type: String,
        required: true,
    },
    data: {
        type: String,
        required: true,
    }, 
    isRead: {
        default: 0,
        type: Number
    }, 
    type: {
        default: 0,
        type: Number
    },

}, {
    timestamps: {
        createdAt: 'createdAt', // Use `created_at` to store the created date
        updatedAt: 'updatedAt' // and `updated_at` to store the last updated date
    }
})

module.exports = mongoose.model('notifications', connectionRequestsSchema)