require('dotenv').config();
const crypto = require('crypto');
const CaptureUserOnPaymentScreen = require('../../models/capture-payment-screen');
const UserModel = require('../../models/user-model');
const axios = require('axios');
const PayU = require("payu-websdk")



const payu_key = process.env.PAYU_LIVE_MERCHANT_KEY; 
const payu_salt = process.env.PAYU_LIVE_SALT;

const PayuClient = new PayU({
    key:payu_key,
    salt:payu_salt
},'PROD')



const generateTransactionId = () => {
    const prefix = "CLKLT";
    const randomString = Math.random().toString(36).substring(2, 10).toUpperCase();
    return `${prefix}${randomString}`;
};


function generateHash(key, txnid, amount, productinfo, firstname, email, salt) {
    const input = `${key}|${txnid}|${amount}|${productinfo}|${firstname}|${email}|||||||||||${salt}`;
    return crypto.createHash('sha512').update(input).digest('hex');
}



const key = process.env.PAYU_LIVE_MERCHANT_KEY;
const amount = process.env.PAYU_BASE_PLAN_AMOUNT;
const productinfo = process.env.PAYU_PRODUCT_INFO;
const salt = process.env.PAYU_LIVE_SALT;
const surl= process.env.PAYU_SURL;
const furl = process.env.PAYU_FURL;
const payuUrl = process.env.PAYU_LIVE_URL


exports.purchaseBasicPlan = async(req, res) => {
    const txnid = generateTransactionId();


    const firstname = req.body.firstName;
    const email = req.body.email;
    const phone = req.body.mobileNumber;
    const hash = generateHash(key, txnid, amount, productinfo, firstname, email, salt);
    
    try{

        if(!firstname || !email || !phone || !hash || !txnid || !key || !amount || !productinfo || !salt || !surl || !furl){
            return res.status(404).json({
                status: false,
                message: 'Invalid Input'
            });
        }


        return res.status(200).json({
            status: true,
            message: 'Payu data',
            data: {
                key,
                amount,
                txnid,
                productinfo,
                salt,
                surl,
                furl,
                hash,
            }
        });

    }catch(error){

        return res.status(500).json({
            status: false,
            message: error.message
        })

    }
}

// this code is to capture the users who clicks on the payment option and see the popup screen on app
exports.captureUserOnPaymentScreen = async (req, res) => {
    const { userId } = req.body;
    try{

        if(!userId){
            return res.status(200).json({
                status: false,
                message: 'userid is required'
            });
        }

        const isValidUser = await UserModel.findById(userId);

        if(!isValidUser){
            return res.status(200).json({
                status: false,
                message: 'Invalid User'
            })
        }

        const existing = await CaptureUserOnPaymentScreen.findOne({ userId });

        if(existing){
            existing.clickCount = existing.clickCount + 1;
            await existing.save();

            return res.status(200).json({
                status: true,
                message: 'Update successfully'
            });
        }

        const newEntry = new CaptureUserOnPaymentScreen({ userId, clickCount: 1 });
        await newEntry.save();

        return res.status(201).json({
            status: true,
            message: 'Created successfully'
        })

    }catch(error){

        return res.status(500).json({
            status: false,
            message: error.message
        })

    }
}

// this code is to list all the user which click on the tls button
exports.listAllUserClickOnPaymentScreen = async (req, res) => {
    try {

    const allData = await CaptureUserOnPaymentScreen.find({});
    const list = []

    for(let data of allData){
        const isUser = await UserModel.findById(data.userId, { first_name: true, last_name: true, _id: false });
        const addedData = { ...isUser.toObject(), clickCount: data.clickCount }

        if(isUser){
            list.push(addedData)
        }
    }

    return res.status(200).json({
        status: true,
        data: list,
        message: 'All list regarding to click on tls'
    });

    }catch(error){
        return res.status(500).json({
            status: false,
            message: error.message
        })
    }
}

// this is for the payment for event part
exports.purchaseEventPlan = async(req, res) => {
    const txnid = generateTransactionId();
    const firstname = req.body.firstName;
    const email = req.body.email;
    const phone = req.body.mobileNumber;
    const eventAmount = req.body.amount
    const hash = generateHash(key, txnid, eventAmount, productinfo, firstname, email, salt);
    
    try{

        if(!firstname || !email || !phone || !hash || !txnid || !key || !eventAmount || !productinfo || !salt || !surl || !furl){
            return res.status(404).json({
                status: false,
                message: 'Invalid Input'
            });
        }

        const response = await axios.post(payuUrl, {
            key, txnid, amount:eventAmount, firstname, email, phone, productinfo, surl, furl, hash
        }, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });

        const success = await response.request.res;

        return res.status(200).json({
            status: true,
            statusCode: success.statusCode,
            redirectUrl: success.responseUrl
        });

    }catch(error){

        return res.status(500).json({
            status: false,
            message: error.message
        })

    }
}



// app.post("/get-payment",

exports.getPayment = async(req,res)=>{
    try {
        const txn_id='KLOUT_CLUB'+Math.floor(Math.random()*8888888)
            const { amount,product,firstname,email,mobile } =req.body

        //    let amount=233
        //    let product = JSON.stringify({
        //     name:'T-shirt',
        //     price:233
        //    })
        //    let firstname='Krishna'
        //    let email="<EMAIL>"
        //    let mobile = 2345678912

            let udf1 = ''
            let udf2 = ''
            let udf3 = ''
            let udf4 = ''
            let udf5 = ''

            const hashString = `${payu_key}|${txn_id}|${amount}|${JSON.stringify(product)}|${firstname}|${email}|${udf1}|${udf2}|${udf3}|${udf4}|${udf5}||||||${payu_salt}`;
            // console.log(hashString);
            

// Calculate the hash
const hash = crypto.createHash('sha512').update(hashString).digest('hex');

       const data=    await PayuClient.paymentInitiate({
   
            
                    isAmountFilledByCustomer:false,
                        txnid:txn_id,
                        amount:amount,
                        currency: 'INR',
                        productinfo:JSON.stringify(product),
                        firstname:firstname,
                        email:email,
                        phone:mobile,
                        surl:`https://app.klout.club/api/v1/payment/verify/${txn_id}`,
                        furl:`https://app.klout.club/api/v1/payment/verify/${txn_id}`,
                        hash
            

            }) 
            res.send(data)
    } catch (error) {
                res.status(400).send({
                    msg:error.message,
                    stack:error.stack
                })
    }
}


//app.post("/verify/:txnid",


exports.verifyPayment  = async(req,res)=>{
// res.send("Done")


const verified_Data = await PayuClient.verifyPayment(req.params.txnid);
const data = verified_Data.transaction_details[req.params.txnid]

res.redirect(`https://organiser.klout.club/payment/${data.status}/${data.txnid}`)
// res.redirect(`http://localhost:5173/payment/${data.status}/${data.txnid}`)
// res.send({
//     status:data.status,
//     amt:data.amt,
//     txnid:data.txnid,
//     method:data.mode,
//     error:data.error_Message,
//     created_at:new Date(data.addedon).toLocaleString()
// })
// PAYU_MONEY_4996538
}



exports.getPaymentEmbed = async(req,res)=>{
    try {
        const txn_id='KLOUT_CLUB'+Math.floor(Math.random()*8888888)
            const { amount,product,firstname,email,mobile } =req.body

        //    let amount=233
        //    let product = JSON.stringify({
        //     name:'T-shirt',
        //     price:233
        //    })
        //    let firstname='Krishna'
        //    let email="<EMAIL>"
        //    let mobile = 2345678912

            let udf1 = ''
            let udf2 = ''
            let udf3 = ''
            let udf4 = ''
            let udf5 = ''

            const hashString = `${payu_key}|${txn_id}|${amount}|${JSON.stringify(product)}|${firstname}|${email}|${udf1}|${udf2}|${udf3}|${udf4}|${udf5}||||||${payu_salt}`;
            // console.log(hashString);
            

// Calculate the hash
const hash = crypto.createHash('sha512').update(hashString).digest('hex');

       const data=    await PayuClient.paymentInitiate({
   
            
                    isAmountFilledByCustomer:false,
                        txnid:txn_id,
                        amount:amount,
                        currency: 'INR',
                        productinfo:JSON.stringify(product),
                        firstname:firstname,
                        email:email,
                        phone:mobile,
                        surl:`https://app.klout.club/api/v1/payment/embed-verify/${txn_id}`,
                        furl:`https://app.klout.club/api/v1/payment/embed-verify/${txn_id}`,
                        hash
            

            }) 
            res.send(data)
    } catch (error) {
                res.status(400).send({
                    msg:error.message,
                    stack:error.stack
                })
    }
}

exports.verifyPaymentEmbed  = async(req,res)=>{
    // res.send("Done")
    
    
    const verified_Data = await PayuClient.verifyPayment(req.params.txnid);
    const data = verified_Data.transaction_details[req.params.txnid]
    
    res.redirect(`https://organiser.klout.club/embed/payment/${data.status}/${data.txnid}`)
    // res.redirect(`http://localhost:5173/embed/payment/${data.status}/${data.txnid}`)
    // res.send({
    //     status:data.status,
    //     amt:data.amt,
    //     txnid:data.txnid,
    //     method:data.mode,
    //     error:data.error_Message,
    //     created_at:new Date(data.addedon).toLocaleString()
    // })
    // PAYU_MONEY_4996538
}

exports.walletTopUp = async (req, res) => {

    // console.log("hello");

    
    try {
        const { amount, firstname, email, mobile, uuid, token } = req.body;

        if (!amount || !firstname || !email || !mobile || !uuid || !token) {
            return res.status(400).json({
                status: false,
                message: 'Missing required fields'
            });
        }

        // const txnid = 'WALLET_' + Math.floor(Math.random() * 8888888);
        // const udfFields = ['', '', '', '', ''];

        const txnid='KLOUT_CLUB'+Math.floor(Math.random()*8888888)
        let udf1 = ''
            let udf2 = ''
            let udf3 = ''
            let udf4 = ''
            let udf5 = ''

            const hashString = `${payu_key}|${txnid}|${amount}|WalletTopUp|${firstname}|${email}|${udf1}|${udf2}|${udf3}|${udf4}|${udf5}||||||${payu_salt}`;
        // const hashString = `${payu_key}|${txn_id}|${amount}|WalletTopUp|${firstname}|${email}|${udfFields.join('|')}||||||${payu_salt}`;
        const hash = crypto.createHash('sha512').update(hashString).digest('hex');

        const data = await PayuClient.paymentInitiate({

            isAmountFilledByCustomer: false,
            txnid: txnid,
            amount: amount,
            currency: 'INR',
            productinfo: "WalletTopUp", // or keep JSON.stringify(product) if dynamic
            firstname: firstname,
            email: email,
            phone: mobile,
            surl: `https://quantamcoder.space/api/v1/payment/verify-wallet-payment/${txnid}/${uuid}/${token}`,
            furl: `https://quantamcoder.space/api/v1/payment/verify-wallet-payment/${txnid}/${uuid}/${token}`,
            hash
        });
        res.send(data);
        

    } catch (error) {
        return res.status(500).json({
            status: false,
            message: error.message
        });
    }
};


exports.verifyWalletPayment = async (req, res) => {
    try {
        const txnid = req.params.txnid;
        const uuid = req.params.uuid;
        const token = req.params.token;



        const verifiedData = await PayuClient.verifyPayment(txnid);
        const txnData = verifiedData.transaction_details[txnid];

        // console.log(txnData);

        // console.log(txnData.status);
        // console.log(Number(txnData.amt));

        if (txnData.status === 'success') {
            // Securely call Laravel API to top up wallet
            const walletApiUrl = `https://kettleworld.space/api/wallet-balance/${uuid}`;

            const response = await axios.post(walletApiUrl, {
                amount: Number(txnData.amt),
                payment_mode: txnData.mode
            }, {
                headers: {
                    Authorization: `Bearer ${token}`, // Pass Bearer token
                    'Content-Type': 'application/json'
                }
            });

            // console.log(response.data);

            return res.redirect(`https://beta-organiser.netlify.app/wallet/success?txnid=${txnid}`);
        } else {
            return res.redirect(`https://beta-organiser.netlify.app/wallet/failed?txnid=${txnid}`);
        }

    } catch (error) {
        return res.status(500).json({
            status: false,
            message: 'Payment verification or wallet top-up failed',
            error: error.message
        });
    }
};