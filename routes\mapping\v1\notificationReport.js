const express = require('express');
const router = express.Router();
const notificationReportController = require('../../../controllers/v1/mapping/notificationReport');


router
    .get('/test', notificationReportController.test)
    .get('/send-connection-request', notificationReportController.sendConnectionRequestReport)
    .get('/accept-connection-request', notificationReportController.acceptConnectionReport)
    .get('/profile-view', notificationReportController.profileViewReport)
module.exports = router