const mongoose = require('mongoose');
const { Schema } = mongoose;

const connectionRequestsSchema = new mongoose.Schema({
    request_user_id: { 
        type:mongoose.Schema.ObjectId,
        required: true,
    }, 
    receive_user_id: { 
        type: mongoose.Schema.ObjectId,
        required: true,
    },
    
    /**
     * status :: 1-pending, 2-accept, 3-Reject
     */
    status: {
        type: Number
    }, 
    
}, {
    timestamps: {
        createdAt: 'createdAt', // Use `created_at` to store the created date
        updatedAt: 'updatedAt' // and `updated_at` to store the last updated date
    }
})

module.exports = mongoose.model('connection_requests', connectionRequestsSchema)