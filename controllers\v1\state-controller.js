const { sendResponse } = require("../../utils/utility");
const State = require("../../models/state-model");


const getStatesByCountryId = async (req, res) => {
  try {
    const { countryId } = req.params;
    const states = await State.find({ country_id: countryId });

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "All States By Country ID",
      data: states,
    });
  } catch (error) {
   return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
      data: error,
    });
  }
};

const getAll = async (req, res) => {
  //statesData.forEach(async (state) => await new State(state).save());
  try {
    const states = await State.find();
    // const result = await State.deleteMany({status: 1 });

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "All States",
      data: states,
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
      data: error,
    });
  }
};

const create = async (req, res) => {
  const { city, isPrimary, isPopular } = req.body;

  // Validate user input
  if (!city) {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "city name is required!",
      data: exist,
    });
  }

  const exist = await CityModel.findOne({ city });

  if (exist) {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "City Name Already Exist, Please try with other name",
      data: exist,
    });
  }

  const data = new CityModel({
    city: city,
    isPopular: isPopular,
    isPrimary: isPrimary,
  });

  try {
    const cityData = await data.save();
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "City add successfully!",
      data: cityData,
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: error.message,
      data: error,
    });
  }
};

const search = async (req, res) => {
  try {
    let query = {};
    const string = req?.body?.string;
    if (string) {
      query.skill = { $regex: string, $options: "i" };
    } else {
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "City list retrieved successfully.",
        data: {},
      });
    }
    query.status = 1;
    const data = await searchData(query);
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "City list retrieved successfully.",
      data: data,
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
      data: error,
    });
  }
};

const details = async (req, res) => {
  try {
    const { id } = req.body;
    // Validate user input
    if (!id) {
      return await sendResponse(req, res, {
        statusCode: 400,
        msg: "City id is required!",
      });
    }
    // const data = await CityModel.findOne({ _id: id }, { __v: false });
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Details  retrieved successfully",
      data: data,
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
      data: error,
    });
  }
};

const searchData = async (query) => {
  return await CityModel.find(query, { __v: false });
};

module.exports = {
  create,
  getAll,
  search,
  details,
  getStatesByCountryId,
};
