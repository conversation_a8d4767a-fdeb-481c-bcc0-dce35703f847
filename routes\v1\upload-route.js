const express = require('express');
const router = express.Router();
//const auth = require("../middleware/auth");
const { v4: uuidv4 } = require('uuid');
// const {sendResponse} = require("../../utils/utility");


const AWS_ACCESS_KEY_ID = process.env.AWS_ACCESS_KEY_ID;
const AWS_ACCESS_KEY_SECRET = process.env.AWS_ACCESS_KEY_SECRET;
const AWS_BUCKET_NAME = process.env.AWS_BUCKET_NAME;

var AWS = require('aws-sdk');

// http://ec2-65-0-180-14.ap-south-1.compute.amazonaws.com/
// api/v1/upload/download-image?imageKey=
// klout/uploads/users/profile_images/e3d7d7ff-bf58-46e3-ab28-e0f062a0b5b5.jpg

//const upload = multer({dest: "uploads/"});

// router.get('/download-image', async (req, res) => {

//     const imageKey = req.body.imageKey || req.query.imageKey;
    
//     if (!imageKey) {
//         res.send({
//             status: 200,
//             success: true,
//             message: "failed to fetech image."
//         })
//     }

//     AWS.config.update({
//         accessKeyId: AWS_ACCESS_KEY_ID,
//         secretAccessKey: AWS_ACCESS_KEY_SECRET,
//     })

//     const s3 = new AWS.S3();
//     var params = { Bucket: AWS_BUCKET_NAME, Key: imageKey };

//     s3.getObject(params, function (err, data) { 
//         res.writeHead(200, { 'Content-Type': 'image/jpeg' });
//         res.write(data.Body, 'binary');
//         res.end(null, 'binary');
//     });
// });


router.get('/download-image', async (req, res) => {


    try{

        const imageKey = req.body.imageKey || req.query.imageKey;
        
        if (!imageKey) {
            res.send({
                status: 200,
                success: true,
                message: "failed to fetech image."
            })
        }

        AWS.config.update({
            accessKeyId: AWS_ACCESS_KEY_ID,
            secretAccessKey: AWS_ACCESS_KEY_SECRET,
        })

        const s3 = new AWS.S3();
        var params = { Bucket: AWS_BUCKET_NAME, Key: imageKey };

        s3.getObject(params, function (err, data) { 
            res.writeHead(200, { 'Content-Type': 'image/jpeg' });
            res.write(data.Body, 'binary');
            res.end(null, 'binary');
        });

    }catch(error){
        console.log(error.message)
    }
});

router.post('/image', async (req, res) => {

    AWS.config.update({
        accessKeyId: AWS_ACCESS_KEY_ID,
        secretAccessKey: AWS_ACCESS_KEY_SECRET,
    })

    const s3 = new AWS.S3();

    const fileContent = Buffer.from(req.files.image.data, 'binary');
    var fileExt = req.files.image.name.split('.').pop();

    const params = {
        Bucket: "klout-image/klout/uploads/users/profile_images",
        Key: uuidv4() + '.' + fileExt,
        Body: fileContent,
    }

    s3.upload(params, (err, data) => {
        if (err) {
            throw err;
        }
        res.send({
            status: 200,
            success: true,
            data
        })
    });
});

module.exports = router;