const mongoose = require('mongoose');
const peopleSchema = new mongoose.Schema({
    firstName: {
        type: String,
        reqiured: true
    },
    lastName: {
        type: String,
    },
    linkedinUrl: {
        type: String
    },
    designation: {
        type: String
    },
    company: {
        type: String
    },
    industry: {
        type: String
    },
    email: {
        type: String
    },
    mobileNumber: {
        type: Number
    },
    city: {
        type: String
    }
}, { timestamps: true })

module.exports = mongoose.model('people', peopleSchema);