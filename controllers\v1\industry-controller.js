const { sendResponse } = require("../../utils/utility");
const IndustryModel = require("../../models/industry-model");

const create = async (req, res) => {

  const { industryName } = req.body;

  // Validate user input
  if (!industryName) {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Industry Name is required.",
      data: {},
    });
  }

  const exist = await IndustryModel.findOne({ industryName });

  if (exist) {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Industry Name Already Exist, Please try with other name",
      data: exist,
    });
  }

  try {
    const industry = await insertIndustryData(req);
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Industry add successfully!",
      data: industry,
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
      data: error,
    });
  }
};

const getlist = async (req, res) => {
  try {
    const data = await findAll(req);

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Industry list retrieved successfully.",
      data: data,
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
      data: error,
    });
  }
};

const search = async (req, res) => {
  try {
    let query = {};

    const string = req?.body?.string;

    if (string) {
      query.industryName = { $regex: string, $options: "i" };
    }

    query.status = 1;

    const data = await searchData(query);

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Industry list retrieved successfully.",
      data: data,
    });

  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
      data: error,
    });
  }
};

const details = async (req, res) => {
  try {
    let query = {};
    const id = req?.body?.id;

    query._id = id;

    const data = await findOne(query);

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Industry details retrieved successfully.",
      data: data,
    });

  } catch (error) {

    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
      data: error,
    });

  }
};

const insertIndustryData = async (req) => {

  const data = new IndustryModel({
    industryName: req.body.industryName,
    status: req.body.status,
  });

  return await data.save();
};

const findAll = async () => {
  return await IndustryModel.find({ status: 1 });
};

const searchData = async (query) => {
  return await IndustryModel.find(query, { __v: false });
};

const findOne = async (query) => {
  return await IndustryModel.findOne(query, { __v: false });
};

const findbyIndustryName = async (industryName) => {
  return await IndustryModel.findOne({ industryName });
};

module.exports = {
  create,
  getlist,
  search,
  details,
  findbyIndustryName,
  insertIndustryData,
  findAll,
};
