const mongoose = require('mongoose');
const { Schema } = mongoose;

const userSchema = new mongoose.Schema({
    connectionId: {
        required: true,
        type: String //{ type: Schema.Types.ObjectId, ref: 'cities' },
    },
    fromUserId: {
        required: true,
        type: String
    },
    toUserId: {
        required: true,
        type: String
    },
    lastMessage: {
        type: String
    },
    lastMessageDateTime: {
        type: String
    },
    unreadCount: {
        type: Number,
        default: 0
    },
    muteStatus: {
        type: Number,
        default: 0
    },
    pinedStatus: {
        type: Number,
        default: 0
    },
    isDeleted: {
        type: Number,
        default: 0
    },
}, {
    timestamps: {
        createdAt: 'createdAt', // Use `created_at` to store the created date
        updatedAt: 'updatedAt' // and `updated_at` to store the last updated date
    }
})

module.exports = mongoose.model('user_chat', userSchema)