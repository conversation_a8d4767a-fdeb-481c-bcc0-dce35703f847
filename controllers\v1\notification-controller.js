const { sendResponse, distanceBt2Points } = require("../../utils/utility");
const { FCMMessaging } = require("../../utils/firebase-notification");
const UserModel = require("../../models/user-model");
const Notification = require("../../models/notifications");
const moment = require("moment");
const ProfileView = require("../../models/profile-view");
const imageBaseUrl = process.env.DOWNLOAD_IMAGE_BASE_URL;

const allList = async (req, res) => {
    
  try {
    const id = req.headers["UserId"] || req.headers["userid"];

    const data = await Notification.find({ user_id: id }, { __v: false }).sort({
      createdAt: -1,
    });

    var dataList = [];

    for (const key of Object.keys(data)) {
      try {
        dataList.push(data[key].from_user_id);
      } catch (error) {
        console.log(error);
      }
    }
    const userProfiles = await UserModel.find(
      { _id: dataList, isDeactivate: 0 },
      {
        __v: false,
        cityId: false,
        first_name: false,
        last_name: false,
        emailId: false,
        mobileNumber: false,
        company: false,
        designation: false,
        industryId: false,
        location: false,
        linkedInId: false,
        linkedInAccessToken: false,
        deviceToken: false,
        deviceVersion: false,
        appVersion: false,
        deviceType: false,
        deviceName: false,
        latitude: false,
        longitude: false,
        city: false,
        status: false,
        password: false,
        whatsAppNotifications: false,
        shareLastSeen: false,
        searchDistanceinKm: false,
        industryName: false,
        createdAt: false,
        updatedAt: false,
        professionalHighlight: false,
        awards: false,
        featured: false,
        skills: false,
        profileImage: false,
        images: false,
      }
    ).sort({ createdAt: -1 });

    // console.log(dataList);
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Lists retrieved successfully",
      data: { data, imageBaseUrl, userProfiles },
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
};

const markStatusRead = async (req, res) => {
  try {
    const id = req.headers["UserId"] || req.headers["userid"];

    const request = {
      isRead: 1,
    };

    const result = await Notification.updateMany(
      { user_id: id },
      {
        $set: request,
      }
    );

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Marked read successfully",
      data: {},
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
};

const unreadCount = async (req, res) => {
  try {
    const id = req.headers["UserId"] || req.headers["userid"];

    const data = await Notification.find(
      { user_id: id, isRead: 0 },
      { __v: false }
    );

    var count = data ? data.length : 0;

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Lists retrieved successfully",
      data: { count, imageBaseUrl },
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
};

const viewProfileNotify = async (req, res) => {
  try {
    const id = req.headers["UserId"] || req.headers["userid"];

    const { toUserId } = req.body;

    var startTime = moment().subtract(1, "w").format("YYYY-MM-DD 00:00:00");

    var endTime = moment().format("YYYY-MM-DD 23:59:59");

    var isNotified = await Notification.find(
      {
        user_id: toUserId,
        from_user_id: id,
        type: 3,
        createdAt: {
          $gt: startTime,
          $lt: endTime,
        },
      },
      { __v: false }
    );

    if (isNotified.length == 0) {
      var fromUserData = await UserModel.findOne({ _id: id }, { __v: false });

      var toUserData = await UserModel.findOne(
        { _id: toUserId },
        { __v: false }
      );

      if (!fromUserData || !toUserData) {
        return await sendResponse(req, res, {
          successStatus: false,
          statusCode: 400,
          msg: "User Id is missing.",
        });
      }
      /** Save Notification    */
      var notificationInsertArr = {
        user_id: toUserData._id,
        from_user_id: id,
        title: fromUserData.first_name + ", " + fromUserData.designation,
        body: "Has viewed your profile",
        isRead: 0,
        type: 3,
        data: JSON.stringify({
          Type: "3",
          Title: fromUserData.first_name + ", " + fromUserData.designation,
          Body: "Has viewed your profile",
          ConnectionId: toUserData._id,
          RequesterId: fromUserData._id,
          RequestId: "",
          Name: fromUserData.first_name,
          Designation: fromUserData.designation,
          ProfileImage: fromUserData.profileImage,
        }),
      };
      const NotificationData = new Notification(notificationInsertArr);

      const NotificationDataArr = await NotificationData.save();

      //ProfileViewTrack
      var ProfileViewTrack = {
        ConnectionId: toUserData._id,
        RequesterId: fromUserData._id,
        View: 1,
        ModifiedDate: new Date(),
        };
      const ProfileViewData = new ProfileView(ProfileViewTrack);
      await ProfileViewData.save();

      if (toUserData && toUserData.deviceToken) {
        /** FCM Notification */
        const FCMMessage = {
          message: {
          //this may vary according to the message type (single recipient, multicast, topic, et cetera)

          token: toUserData.deviceToken,

          notification: {
            title: notificationInsertArr.title,
            body: notificationInsertArr.body,
          },
          data: {
            Type: "3",
            Title: fromUserData.first_name + ", " + fromUserData.designation,
            Body: "Has viewed your profile",
            ConnectionId: toUserData._id,
            RequesterId: fromUserData._id,
            RequestId: "",
            Name: fromUserData.first_name,
            Designation: fromUserData.designation,
            ProfileImage: fromUserData.profileImage,
          }},
        };
        FCMMessaging(FCMMessage);
      }
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "Marked read successfully",
        data: {},
      });
    } else {
      const fromUserData = await UserModel.findOne({ _id: id }, { __v: false });
      const toUserData = await UserModel.findOne(
        { _id: toUserId },
        { __v: false }
      );

      const existedProfileView = await ProfileView.findOne({
        ConnectionId: toUserData._id,
        RequesterId: fromUserData._id,
      })

      if(existedProfileView){
        existedProfileView.ModifiedDate = new Date();
        await existedProfileView.save();
      }
      
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 200,
        msg: "Already sent.",
        data: {},
      });
    }
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
};


// 18-10-2024
const filterList = async(req, res) => {
  const { user_id, from_user_id, type } = req.body;
  try{
    const list = await Notification.find({ user_id, from_user_id, type });

    return res.status(200).json({
      status: true,
      data: list,
      message: 'Filtered List'
    });

  }catch(error){
    return res.status(400).json({
      status: false,
      message: error.message
    })
  }
}

module.exports = {
  viewProfileNotify,
  allList,
  markStatusRead,
  unreadCount,
  filterList
};
