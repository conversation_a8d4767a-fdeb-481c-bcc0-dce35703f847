const mongoose = require('mongoose');

const dataSchema = new mongoose.Schema({
    country: String,
    state: String,
    skill: String,
    company: String,
    industry: String,
    designation: String,
    employeeSize: String,
    priority: String
})

const uploadICPSchema = new mongoose.Schema({
    userId: {
        type: String,
        required: true
    },
    // country: {
    //     type: String,
    //     required: true
    // },
    // countries: [String],
    // states: [String],
    // skills: [String],
    // companies: [String],
    // industries: [String],
    // jobTitles: [String],
    // employeeSizes: [String]
    list: [dataSchema]
});



module.exports = mongoose.model('upload_icp', uploadICPSchema);