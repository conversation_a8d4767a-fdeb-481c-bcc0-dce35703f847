const express = require("express");
const router = express.Router();
const uploadFile = require("../../../middleware/upload");
const auth = require("../../../middleware/auth");
const AdminController = require("../../../controllers/v1/marketing/admin-controller");
const multer = require('multer');
const app = express();
const path = require('path');
const fileUpload = require('express-fileupload');

app.use(fileUpload());

app.use(express.urlencoded({ extended: false }))

// ------------------------------------------------------------
// const storage = multer.diskStorage({
//   destination: function (req, file, cb) {
//     cb(null, 'uploads/');
//   },
//   filename: function (req, file, cb) {
//     cb(null, Date.now() + '-' + file.originalname);
//   }
// });

// const upload = multer({ storage: storage, limits: { fileSize: 50 * 1024 * 1024 }  });
router.post('/uploadICP', async(req, res) => {
  const exist = await AdminController.uploadICP(req, res);
});

// --------------------------------------

// "images" are the folder name
const fileStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads');
  },
});

app.use(multer({ storage: fileStorage }).single('file'));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

router.post("/upload-users", uploadFile.single("file"), async (req, res) => {
  await AdminController.uploadUsers(req, res);
});

router.get("/sendInterakartMessage", async (req, res) => {
  await AdminController.sendInterakartMessage(req, res);
});

//Test API
router.get("/test", async (req, res) => {
  await AdminController.test(req, res);
});

//User Login
router.post("/login", async (req, res) => {
  await AdminController.login(req, res);
});

//User Logout
router.get("/logout", auth, async (req, res) => {
  await AdminController.logout(req, res);
});

router.get("/checkingAuthenticated", auth, async (req, res) => {
  await AdminController.checkingAuthenticated(req, res);
});

router.post("/details", auth, async (req, res) => {
  await AdminController.userView(req, res);
});

router.post("/register", async (req, res) => {
  await AdminController.register(req, res);
});

//Super Admin Users

router.post("/admin-user-list", auth, async (req, res) => {
  await AdminController.adminUserList(req, res);
});

router.post("/admin-view-user-list", auth, async (req, res) => {
  await AdminController.adminViewUser(req, res);
});

router.post("/admin-delete-user/:userId", auth, async (req, res) => {
  await AdminController.deleteAdminUser(req, res);
});

router.get("/get-requested-points-user", auth, async (req, res) => {
  await AdminController.getRequestedPointsUser(req, res);
});

//Approved Points
router.get("/approved-requested-points-user", auth, async (req, res) => {
  await AdminController.approvedRequestedPointsUser(req, res);
});

//Decline Points
router.get("/decline-requested-points-user", auth, async (req, res) => {
  await AdminController.declineRequestedPointsUser(req, res);
});


router.get("/user-list", async (req, res) => {
  await AdminController.userList(req, res);
});

router.post("/add-sub-admin", auth, async (req, res) => {
  await AdminController.addSubAdmin(req, res);
});

router.get("/admin-sub-admin-list", auth, async (req, res) => {
  await AdminController.adminSubAdminList(req, res);
});

router.get("/get-sub-admin-user-details/:userId", async (req, res) => {
  await AdminController.getSubAdminUserDetails(req, res);
});

//Update Subadmin User
router.post("/updateSubAdminUser", auth, async (req, res) => {
  await AdminController.updateSubAdminUser(req, res);
});

//delete Subadmin User
router.delete("/:userId", auth, async (req, res) => {
  await AdminController.deleteSubAdminUser(req, res);
});

// Get user details by ID
router.get("/:id", auth, async (req, res) => {
  await AdminController.userDetails(req, res);
});

// Update user details
// router.put("/:id", async (req, res) => {
//   await AdminController.updateUser(req, res);
// });

router.delete("/:id", auth, async (req, res) => {
  await AdminController.deleteUser(req, res);
});

router.post("/getUserPoints", auth, async (req, res) => {
  await AdminController.getUserPoints(req, res);
});

router.post("/allocatePoints", auth, async (req, res) => {
  await AdminController.allocatedPoints(req, res);
});

//Buy Credits
router.post("/buyCredits", auth, async (req, res) => {
  await AdminController.buyCredits(req, res);
});

//Get Buy Credits List
router.post("/buyCreditsList", auth, async (req, res) => {
  await AdminController.buyCreditsList(req, res);
});

//Appoved Credit to User
router.post("/approvedCredits", auth, async (req, res) => {
  await AdminController.approvedCredits(req, res);
});

router.post("/getTotalContactViews", async (req, res) => {
  await AdminController.getTotalContactViews(req, res);
});

router.post("/addContactViews", async (req, res) => {
  await AdminController.addContactViews(req, res);
});

router.post("/getTotalChatMade", async (req, res) => {
  await AdminController.getTotalChatMade(req, res);
});

router.post("/checkContactMadePoints", async (req, res) => {
  await AdminController.checkContactMadePoints(req, res);
});

router.post("/checkChatMadePoints", async (req, res) => {
  await AdminController.checkChatMadePoints(req, res);
});

router.post("/addChatMade", async (req, res) => {
  await AdminController.addChatMade(req, res);
});

router.post("/get-all-users-points", auth, async (req, res) => {
  await AdminController.getAllUsersConnectPoints(req, res);
});

router.post("/user-chat-details", async (req, res) => {
  await AdminController.userChatDetails(req, res);
});

router.post("/sub-admin-chat-user-list", async (req, res) => {
  await AdminController.subAdminChatUserList(req, res);
});

router.post("/checkContactMadePoints", async (req, res) => {
  await AdminController.checkContactMadePoints(req, res);
});

router.post("/checkChatMadePoints", async (req, res) => {
  await AdminController.checkChatMadePoints(req, res);
});

router.post("/addChatMade", async (req, res) => {
  await AdminController.addChatMade(req, res);
});

router.post("/get-all-users-points", auth, async (req, res) => {
  await AdminController.getAllUsersConnectPoints(req, res);
});

router.post("/user-chat-details", async (req, res) => {
  await AdminController.userChatDetails(req, res);
});

router.post("/sub-admin-chat-user-list", async (req, res) => {
  await AdminController.subAdminChatUserList(req, res);
});

//saveICP
router.post("/saveICP", auth, async (req, res) => {
  await AdminController.saveICP(req, res);
});

//getICP
router.post('/getICPData', auth, async(req, res) => {
  await AdminController.getICPData(req, res);
}); 

router.post('/getUploadedICPData', auth, async(req, res) => {
  await AdminController.getUploadedICPData(req, res);
}); 


//Marketing Tracking
router.post("/SendRequestTrackDetails", async (req, res) => {
  await AdminController.SendRequestTrackDetails(req, res);
});

router.post("/ProfileViewTrackDetails", async (req, res) => {
  await AdminController.ProfileViewTrackDetails(req, res);
});

router.post("/ChatTrackDetails", async (req, res) => {
  await AdminController.ChatTrackDetails(req, res);
});

// profile view detail 
router.post("/profileVeiwDetails", async (req, res) => {
  await AdminController.profileVeiwDetails(req, res);
});

// chat view details
router.post("/chatViewDetails", async (req, res) => {
  await AdminController.chatViewDetails(req, res);
});
// user by email or phone
router.post("/userByEmailOrPhone", async (req, res) => {
  await AdminController.userByEmailOrPhone(req, res);
});

//  assign to subadmin
router.post("/assignSubadmin", async (req, res) => {
  await AdminController.assignSubadmin(req, res);
});

//assign to user
router.post("/assignToUser", async (req, res) => {
  await AdminController.assignUser(req, res);
});


// connection details
router.post("/connectionsDetail", async (req, res) => {
  await AdminController.connectionsDetail(req, res);
});

module.exports = router;
