const express = require('express');
const SkillsModel = require('../../models/skill-model');
const router = express.Router();
const auth = require("../../middleware/auth");
const {sendResponse} = require("../../utils/utility");

//Post Method
router.post('/add', async (req, res) => {
    const { skill,skillDesc } = req.body;

    // Validate user input
    if (!(skill && skillDesc)) {
        return await sendResponse( req, res,{statusCode:400, msg : "skill and  Description required!"})
    }
    const exist = await SkillsModel.findOne({ skill });
    if (exist) {
     return await sendResponse( req, res,{statusCode:409, msg : "Skill Name Already Exist, Please try with other name", data: exist})
    }
    const data = new SkillsModel({
        skill: req.body.skill,
        skillDesc: req.body.skillDesc,
    })
    try {
        const SkillsData = await data.save();
        return await sendResponse( req, res,{statusCode:200, msg : "Skill add successfully!", data: SkillsData})
    }
    catch (error) {
        return await sendResponse( req, res,{statusCode:400, msg : error.message, data: error})
    }
})

//Get all Method
router.get('/getAll', async (req, res) => {
    try {
        const data = await SkillsModel.find();
        return await sendResponse( req, res,{statusCode:200, msg : "Skill list retrieved successfully", data: data})
    }
    catch (error) {
        return await sendResponse( req, res,{statusCode:500, msg : error.message, data: error})
    }
})
//Get one reacords Method
router.get('/findone', async (req, res) => {
    try {
        const { skill } = req.query;

        // Validate user input
        if (!(skill)) {
            return await sendResponse( req, res,{statusCode:400, msg : "skill is required!"})
        }
        
        const data = await SkillsModel.findOne({ skill });
        return await sendResponse( req, res,{statusCode:200, msg : "Skill  retrieved successfully", data: data})
    }
    catch (error) {
        return await sendResponse( req, res,{statusCode:500, msg : error.message, data: error})
    }
})

module.exports = router;