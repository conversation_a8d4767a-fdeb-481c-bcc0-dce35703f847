const mongoose = require("mongoose");

const { Schema } = mongoose;

const profileViewSchema = new mongoose.Schema(
  {
    ConnectionId: {
      required: true,
      type: Schema.Types.ObjectId,
      ref: "user",
    },
    RequesterId: {
      required: true,
      type: Schema.Types.ObjectId,
      ref: "user",
    },
    View: {
      required: true,
      type:String,
    },
    ModifiedDate: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
  }
);

module.exports = mongoose.model("profile_view", profileViewSchema);
