const { sendResponse } = require("../../utils/utility");
const fs = require("fs/promises");

const termsConditions = async (req, res) => {
  try {
    const data = await fs.readFile("./public/termsconditions.txt", {
      encoding: "utf8",
    });
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "",
      data: data,
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: error.message,
      data: error,
    });
  }
};

module.exports = {
  termsConditions,
};
