const express = require('express');
const CityModel = require('../../models/city-model');
const router = express.Router();
const auth = require("../../middleware/auth");
const {sendResponse} = require("../../utils/utility");

//Post Method
router.post('/add', async (req, res) => {
    const { city,is_primary } = req.body;

    // Validate user input
    if (!city) {
        return res.status(400).send({error:"city name is required!"});
    }

    const exist = await CityModel.findOne({ city });

    if (exist) {
     return await sendResponse( req, res,{statusCode:409, msg : "City Name Already Exist, Please try with other name", data: exist})
    }

    const data = new CityModel({
        city: city,
        is_primary:is_primary
    })

    try {
        const cityData = await data.save();
        return await sendResponse( req, res,{statusCode:200, msg : "City add successfully!", data: cityData})
    }catch (error) {
        return await sendResponse( req, res,{statusCode:400, msg : error.message, data: error})
    }
})

//Get all Method
router.get('/getAll', async (req, res) => {
    try {
        let data;
        if(req?.query?.city){
             data = await CityModel.find({city: {$regex: req.query.city, $options: 'i'}});
        }else{
             data = await CityModel.find();
        }
        return await sendResponse( req, res,{statusCode:200, msg : "City list retrieved successfully", data: data})
    }
    catch (error) {
        return await sendResponse( req, res,{statusCode:500, msg : error.message, data: error})
    }
})
//find one  Method
router.get('/findone', async (req, res) => {
    try {
        const { city } = req.query;
         // Validate user input
         if (!city) {
            return await sendResponse( req, res,{statusCode:400, msg : "city is required!"})
        }
        const data = await CityModel.findOne({ city });
        return await sendResponse( req, res,{statusCode:200, msg : "City  retrieved successfully", data: data})
    }
    catch (error) {
        return await sendResponse( req, res,{statusCode:500, msg : error.message, data: error})
    }
})

// Search 
router.get('/search', async (req, res) => {
    try {
        let query = {};
    
        if(req?.query?.city){
             query.city = {$regex: req.query.city, $options: 'i'}
        }

        if(req?.query?.status){
            query.status = req.query.status
        }

        if(req?.query?.is_primary){
            query.is_primary = req.query.is_primary
        }
        
        const data = await CityModel.find(query);
    
        return await sendResponse( req, res,{statusCode:200, msg : "City list retrieved successfully", data: data})
    }
    catch (error) {
        return await sendResponse( req, res,{statusCode:500, msg : error.message, data: error})
    }
})

module.exports = router;