const mongoose = require('mongoose');
const { Schema } = mongoose;

const userSchema = new mongoose.Schema({

    email: {
        type: String
    },
    mobileNumber: {
        type: Number
    },
    otp: {
        type: Number
    },

}, {
    timestamps: {
        createdAt: 'createdAt', // Use `created_at` to store the created date
        updatedAt: 'updatedAt' // and `updated_at` to store the last updated date
    }
})

module.exports = mongoose.model('otp_temp', userSchema)