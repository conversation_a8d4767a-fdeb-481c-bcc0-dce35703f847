const mongoose = require("mongoose");

const { Schema } = mongoose;

const userContactViewSchema = new mongoose.Schema(
  {
    userId: {
      required: true,
      type: Schema.Types.ObjectId,
      ref: "user",
    },
    contactUserId: {
      required: true,
      type: Schema.Types.ObjectId,
      ref: "user",
    },
    contactView: {
      required: true,
      type: String,
    },
    points: {
      type: Number,
      default: 1,
    },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
  }
);

module.exports = mongoose.model("user_contact_view", userContactViewSchema);
