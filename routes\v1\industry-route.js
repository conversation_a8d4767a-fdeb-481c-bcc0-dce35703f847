const express = require('express');
const IndustryController = require('../../controllers/v1/industry-controller');
const router = express.Router();
const auth = require("../../middleware/auth"); 

router.post('/create', auth, async (req, res) => {
    const exist = await IndustryController.create(req, res);
});
router.get('/getlist', async (req, res) => {
    const exist = await IndustryController.getlist(req, res); 
});
router.post('/search', async (req, res) => {
    const exist = await IndustryController.search(req, res); 
});
router.post('/details', async (req, res) => {
    const exist = await IndustryController.details(req, res); 
});

module.exports = router;