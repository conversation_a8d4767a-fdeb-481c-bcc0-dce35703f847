const mongoose = require("mongoose");

const { Schema } = mongoose;

const creditTransactionsSchema = new mongoose.Schema(
  {
    RequestedAdminUserId: {
      required: true,
      type: Schema.Types.ObjectId,
      ref: "user",
    },
    superAdminUserId: {
      required: true,
      type: Schema.Types.ObjectId,
      ref: "user",
    },
    numberOfCredits: {
      required: true,
      type: String,
      default: 0,
    },
    totalAmount: {
      type: String,
      default: 0,
    },
    gst: {
      type: String,
      default: 0,
    },
    approved: {
      type: String,
      default: 0,
    },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
  }
);

module.exports = mongoose.model("credit_transactions", creditTransactionsSchema);
