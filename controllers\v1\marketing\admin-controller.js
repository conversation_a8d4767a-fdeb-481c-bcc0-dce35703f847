const fs = require("fs");
const csv = require("fast-csv");

const csvParser = require('csv-parser');

const { createReadStream, unlink } = require("fs");


const { parse } = require("fast-csv");

const nodemailer = require("nodemailer");
const urlencode = require("urlencode");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const moment = require("moment");
const https = require("https");

const axios = require("axios");

const admin = require("firebase-admin");

const serviceAccount = require("../../../services/FirebaseAccountKey.json");

const { sendResponse, distanceBt2Points } = require("../../../utils/utility");
const { FCMMessaging } = require("../../../utils/firebase-notification");
const xlsx = require('xlsx');

const UserModel = require("../../../models/user-model");
const OTPTemp = require("../../../models/otp-temp");
const userContactViewModel = require("../../../models/user-contact-view-model");
const userChatViewModel = require("../../../models/user-chat-made-model");
const userPointModel = require("../../../models/user-point-model");
const userChatMadeModel = require("../../../models/user-chat-made-model");
const creditTransactions = require("../../../models/credit-transactions");
const profileView = require("../../../models/profile-view");
const chatTrackView = require("../../../models/chat-track-view");
const SendRequestTrackView = require("../../../models/send-request-track-view");
const ConnectionsModel = require("../../../models/connections");
const ICPModel = require("../../../models/ICP");
const UploadICPModel = require('../../../models/uploadICP');


const imageBaseUrl = process.env.DOWNLOAD_IMAGE_BASE_URL;

function generateOTP() {
  return Math.floor(100000 + Math.random() * 900000);
}

const sendWelcomeEmail = async (email, password, userid) => {
  const mailOptions = {
    from: {
      name: "Klout Club Marketing",
      address: process.env.SMTP_EMAIL,
    },
    to: email,
    subject: "UserName and Password",
    html: `
    <p>Your UserName is: <strong>${email}</strong></p>
    <p>Your Password is: <strong>${password}</strong></p>
    <p>Please Click <a href="https://kloutclub.page.link/?link=https://www.klout.club?userid=${userid}&apn=com.klout.app&isi=6475306206&ibi=com.klout.app" target="_blank">here</a> for login.</p>
  `,
  };

  transporter.sendMail(mailOptions, (error, info) => {
    if (error) {
      res.status(500).send({ status: "500", message: "Error sending email." });
    }
    res.status(200).send({ status: "200", message: "OTP sent successfully." });
  });

  try {
    return await transporter.sendMail(mailOptions);
  } catch (error) {
    return false;
  }
};

// const sendInterakartMessage = async (
//   phoneNumber,
//   message,
//   userId = "",
//   callbackData = "some_callback_data"
// )
const sendInterakartMessage = async (req, res) => {
  // const apiKey = process.env.INTERAKT_API_KEY;

  const phoneNumber = "918709289369";
  // const message = "Hello Test";

  // const data = {
  //   userId: "",
  //   fullPhoneNumber: phoneNumber,
  //   type: "Text",
  //   data: {
  //     message: message,
  //   },
  // };

  // Replace with your Interakt API key and phone number
  const INTERAKT_API_KEY = process.env.INTERAKT_API_KEY;
  // const PHONE_NUMBER = "+918709289369"; // Use the format +1234567890

  // Message details
  // const msgData = {
  //   messaging_product: "whatsapp",
  //   to: PHONE_NUMBER,
  //   type: "Text",
  //   text: {
  //     body: "Hello, this is a test message from Interakt using Node.js!",
  //   },
  // };

  // try {
  //   const response = await axios.post(
  //     "https://api.interakt.ai/v1/public/message/",
  //     {msgData},
  //     {
  //       headers: {
  //         Authorization: `Basic ${INTERAKT_API_KEY}`,
  //         "Content-Type": "application/json",
  //       },
  //     }
  //   );

  //   console.log("Message sent:", response.data);
  // } catch (error) {
  //   console.error(
  //     "Error sending message:",
  //     error.response ? error.response.data : error.message
  //   );
  // }
  var data = JSON.stringify({
    userId: "",
    fullPhoneNumber: phoneNumber,
    callbackData: "some_callback_data",
    type: "Text",
    data: {
      message: "This msg is sent via API",
    },
  });

  var config = {
    method: "post",
    maxBodyLength: Infinity,
    url: "https://api.interakt.ai/v1/public/message/",
    headers: {
      Authorization: "Basic ${INTERAKT_API_KEY}",
      "Content-Type": "application/json",
    },
    data: data,
  };

  axios(config)
    .then(function (response) {
      console.log("Reasponse Data", response.data);
      // console.log(JSON.stringify(response.data));
    })
    .catch(function (error) {
      console.log(error);
    });
};

//Upload Users
const uploadUsers = async (req, res) => {
  const { userId } = req.body;

  try {
    if (req.file == undefined) {
      return res.status(400).send("Please upload a CSV file!");
    }

    let employees = [];
    let path = "./resources/static/assets/uploads/" + req.file.filename;

    createReadStream(path)
      .pipe(parse({ headers: true }))
      .on("error", (error) => {
        res.status(500).send("Error parsing CSV File");
      })
      .on("data", (row) => {
        employees.push(row);
      })
      .on("end", async () => {
        let addedUsers = [];
        let duplicateUsers = [];

        for (const row of employees) {
          const oldUserEmail = await UserModel.findOne({
            emailId: row.emailId,
          });
          const oldUserMobile = await UserModel.findOne({
            mobileNumber: row.mobileNumber,
          });

          if (!oldUserEmail && !oldUserMobile) {
            const user = new UserModel({
              first_name: row.first_name,
              last_name: row.last_name,
              role: row.role,
              emailId: row.emailId,
              linkedInId: "",
              googleId: "",
              password: await bcrypt.hash(row.password, 10),
              mobileNumber: row.mobileNumber,
              profileImage: "",
              company: "",
              designation: row.designation,
              industryName: "",
              industryId: "646f65e7605c3bedd07a1013",
              linkedInAccessToken: "",
              deviceToken: "",
              deviceVersion: "",
              deviceType: "",
              deviceName: "",
              appVersion: "",
              images: "",
              latitude: "",
              preferred_skills: "",
              longitude: "",
              city: "",
              cityId: null,
              searchDistanceinKm: 50,
              shareLastSeen: 1,
              whatsAppNotifications: 1,
              status: 1,
              isDeactivate: 0,
              addedBy: userId,
            });

            const userAdded = await user.save();

            addedUsers.push(userAdded);

            if (userAdded) {
              await sendWelcomeEmail(row.emailId, row.password, userAdded._id);
            }
          } else {
            duplicateUsers.push(row.emailId);
          }
        }

        sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "Users processed successfully!",
          addedUsers,
          duplicateUsers,
        });
      });
  } catch (error) {
    console.log(error);
    res.status(500).send({
      status: 500,
      message: "Failed to upload the file: " + req.file.originalname,
      error: error.message,
    });
  }
};

const test = async (req, res) => {
  // const response = await UserModel.deleteMany({});

  if (res) {
    await res
      .status(200)
      .send({ status: "200", message: "Marketing Application Working !!" });
  }
};

function generateOTP() {
  return Math.floor(100000 + Math.random() * 900000);
}

const transporter = nodemailer.createTransport({
  service: "gmail",
  host: "smtp.gmail.com",
  port: 465,
  secure: true,
  auth: {
    user: process.env.SMTP_EMAIL,
    pass: process.env.SMTP_PASSWORD,
  },
});

const verifyToken = (token) => {
  return new Promise((resolve, reject) => {
    jwt.verify(token, process.env.TOKEN_KEY, (err, decoded) => {
      if (err) {
        reject(err);
      } else {
        resolve(decoded);
      }
    });
  });
};

const register = async (req, res) => {

  const { first_name, last_name, email, password, mobile, otp, step } = req.body;

  if (!email) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "Email is required",
    });
  }

  const oldUser = await UserModel.findOne({
    emailId: email,
  });

  if (oldUser) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "User Already Exist. Please Login",
    });
  }

  try {
    let encryptedPassword = "";

    if ((email)) {
      if (step == "1") {
        const otp = generateOTP();

        const data = new OTPTemp({
          email: email,
          mobileNumber: mobile,
          otp: otp,
        });

        const user = await data.save();

        if (user) {
          const mailOptions = {
            from: {
              name: "Klout Club Marketing",
              address: process.env.SMTP_EMAIL,
            },
            to: email,
            subject: "OTP for Registration",
            text: `Your OTP for registration is ${otp}`,
          };

          //Send Phone
          var apikey = "NWE1MTZmMzY2Nzc5NTc1ODRjNDQ3NjU5NmE1YTczNTY=";

          var sender = "INSKLT";

          const content = `${otp} is your OTP for verifying your profile with KloutClub by Insightner Marketing Services`;

          var msg = encodeURIComponent(content);

          var dataOne =
            "apikey=" +
            apikey +
            "&sender=" +
            sender +
            "&numbers=" +
            mobile +
            "&message=" +
            msg;

          var options = {
            host: "api.textlocal.in",
            path: "/send?" + dataOne,
          };

          callback = function (response) {
            var str = "";

            response.on("data", function (chunk) {
              str += chunk;
            });

            response.on("end", function () {
              console.log(str);
            });
          };

          https.request(options, callback).end();

          //Send Mail
          transporter.sendMail(mailOptions, (error, info) => {
            if (error) {
              res
                .status(500)
                .send({ status: "500", message: "Error sending email." });
            }
            res
              .status(200)
              .send({ status: "200", message: "OTP sent successfully." });
          });
        }
      } else if (step == "2") {
        if (!otp) {
          return await sendResponse(req, res, {
            successStatus: false,
            statusCode: 200,
            msg: "OTP is required!",
          });
        }

        const verifyOtp = await OTPTemp.findOne({
          email: email,
          otp: otp,
        });

        if (!verifyOtp) {
          return await sendResponse(req, res, {
            successStatus: false,
            statusCode: 200,
            msg: "OTP verification failed.",
          });
        } else {
          var details = await OTPTemp.deleteOne({
            email: email,
            otp: otp,
          });

          encryptedPassword = await bcrypt.hash(password, 10);

          const data = new UserModel({
            first_name: req.body.first_name,
            last_name: req.body.last_name,
            role: req.body.role,
            emailId: req.body.email,
            linkedInId: "",
            googleId: "",
            password: encryptedPassword,
            mobileNumber: req.body.mobileNumber,
            profileImage: req.body.profileImage,
            company: req.body.company,
            designation: req.body.designation,
            industryName: req.body.industryName,
            industryId: req.body.industryId,
            // location: req.body.location,
            linkedInAccessToken: req.body.linkedInAccessToken,
            deviceToken: req.body.deviceToken,
            deviceVersion: req.body.deviceVersion,
            deviceType: req.body.deviceType,
            deviceName: req.body.deviceName,
            appVersion: req.body.appVersion,
            images: req.body.images,
            latitude: "",
            preferred_skills: "",
            longitude: "",
            city: "",
            cityId: null,
            searchDistanceinKm: 50,
            shareLastSeen: 1,
            whatsAppNotifications: 1,
            status: 1,
            isDeactivate: 0,
          });

          const user = await data.save();

          const maxAge = 7 * 24 * 60 * 60;

          const token = jwt.sign(
            { user_id: user._id, email, role: user.role },
            process.env.TOKEN_KEY
            // {
            //   expiresIn: maxAge,
            // }
          );

          return await sendResponse(req, res, {
            successStatus: true,
            statusCode: 200,
            msg: "Otp Verified & User registered successfully",
            data: { user, token, imageBaseUrl },
          });
        }
      }
    } else {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 200,
        msg: "Please Enter all Fields",
      });
    }
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: error.message,
      data: error,
    });
  }
};

const login = async (req, res) => {
  const { email, password } = req.body;

  if (!email) {
    return res.status(200).json({
      status: false,
      code: 400,
      message: "Email is required.",
    });
  }

  try {
    let user;
    let token;

    if (email && password) {
      const user_details = await UserModel.findOne(
        { emailId: email },
        { __v: false }
      );

      if (!user_details || user_details === null) {
        return res.status(200).json({
          status: false,
          code: 400,
          message: "Invalid Email or Password",
        });
      }

      const passwordMatch = await bcrypt.compare(
        password,
        user_details.password
      );

      if (!passwordMatch) {
        return res.status(200).json({
          status: false,
          code: 400,
          message: "Invalid Password",
        });
      }

      user = user_details;
    }

    if (!user) {
      return res.status(200).json({
        status: false,
        code: 400,
        message: "Invalid Email or Password",
      });
    }

    const maxAge = 7 * 24 * 60 * 60;

    token = jwt.sign(
      { user_id: user._id, email, role: user.role },
      process.env.TOKEN_KEY,
      {
        expiresIn: maxAge,
      }
    );

    await UserModel.updateOne(
      { emailId: email },
      { $set: { isDeactivate: 0 } }
    );

    return sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Logged in successfully!",
      data: { user, token, imageBaseUrl },
    });
  } catch (err) {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: err.message,
    });
  }
};

const forgetPassword = async (req, res) => {
  const { email, otp, step, password } = req.body;

  try {
    if (!email) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "Email is required",
      });
    }

    const user = await UserModel.findOne({ emailId: email });

    if (!user) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "User not Found",
      });
    }

    // const token = jwt.sign({ email }, process.env.TOKEN_KEY, {
    //   expiresIn: "1000h",
    // });

    if (email) {
      if (step == "1") {
        const otp = generateOTP();

        const data = new OTPTemp({
          email: req.body.email,
          otp: otp,
        });

        const user = await data.save();

        if (user) {
          const mailOptions = {
            from: {
              name: "Klout Club Marketing",
              address: process.env.SMTP_EMAIL,
            },
            to: email,
            subject: "Password Reset Password",
            text: `Your OTP for Password Reset is ${otp}`,
          };

          transporter.sendMail(mailOptions, (error, info) => {
            if (error) {
              res
                .status(500)
                .send({ status: false, message: "Error sending email" });
            }

            res
              .status(200)
              .send({ status: true, message: "OTP sent successfully." });
          });
        }
      } else if (step == "2") {
        if (!otp) {
          return await sendResponse(req, res, {
            successStatus: false,
            statusCode: 200,
            msg: "OTP is required!",
          });
        }

        const verifyOtp = await OTPTemp.findOne({
          email: email,
          otp: otp,
        });

        if (!verifyOtp) {
          return await sendResponse(req, res, {
            successStatus: false,
            statusCode: 400,
            msg: "OTP verification failed.",
          });
        }

        var details = await OTPTemp.deleteOne({
          email: email,
          otp: otp,
        });

        return await sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "Otp verified successfully",
          data: { user },
        });
      } else if (step == "3") {
        encryptedPassword = await bcrypt.hash(password, 10);

        const hashedPassword = await bcrypt.hash(password, 10);

        const r = await UserModel.updateOne(
          { emailId: email },
          { $set: { password: hashedPassword } }
        );

        const token = jwt.sign(
          { user_id: user._id, email },
          process.env.TOKEN_KEY
        );

        return await sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "User password reset successfully",
          data: { user, token, imageBaseUrl },
        });
      }
    }
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: error.message,
      data: error,
    });
  }
};

const userView = async (req, res) => {
  try {
    const userId = req.user.user_id;

    //Admin
    var user = await UserModel.findOne(
      { _id: userId },
      { password: false, __v: false }
    );

    // const user = await UserModel.findById(userId).select("-password");

    if (!user) {
      return sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "User not found",
      });
    } else {
      const subAdminUserId = req.body.memberId;

      var subadmin = await UserModel.findOne(
        { _id: subAdminUserId },
        { password: false, __v: false }
      );

      return sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "User details retrieved successfully",
        data: { subadmin },
      });
    }
  } catch (err) {
    // console.error(err);
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: "Internal server error",
    });
  }
};

const logout = async (req, res) => {
  const token = req.headers["x-access-token"];

  if (!token) {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "Token is required",
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.TOKEN_KEY);
    const userId = decoded.user_id;

    await UserModel.updateOne({ _id: userId }, { $set: { isDeactivate: 1 } });

    return sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Logged out successfully!",
    });
  } catch (err) {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: "Failed to logout",
    });
  }
};

const addSubAdmin = async (req, res) => {
  const token = req.headers["x-access-token"];

  if (!token) {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "Unauthorized User",
    });
  }

  const decoded = await verifyToken(token.replace("Bearer ", ""));
  const userId = decoded.user_id;

  const {
    first_name,
    last_name,
    emailId,
    password,
    mobileNumber,
    role,
    company,
    designation,
  } = req.body;

  if (!emailId) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "Email is required",
    });
  }

  if (!first_name) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "First Name is required",
    });
  }

  if (!mobileNumber) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "Mobile Number  is required",
    });
  }

  const oldUser = await UserModel.findOne({
    emailId: emailId,
  });

  if (oldUser) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "Email Already Exist. Please Login",
    });
  }

  const oldUserMobile = await UserModel.findOne({
    mobileNumber: mobileNumber,
  });

  if (oldUserMobile) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "Mobile Number Already Exist. Please Login",
    });
  }

  if (first_name && last_name && emailId && password && role) {
    const user = new UserModel({
      first_name: first_name,
      last_name: last_name,
      role: role,
      emailId: emailId,
      linkedInId: "",
      googleId: "",
      password: await bcrypt.hash(password, 10),
      mobileNumber: mobileNumber,
      profileImage: "",
      company: company,
      designation: designation,
      industryName: "",
      industryId: "646f65e7605c3bedd07a1013",
      //location: data.location,
      linkedInAccessToken: "",
      deviceToken: "",
      deviceVersion: "",
      deviceType: "",
      deviceName: "",
      appVersion: "",
      images: "",
      latitude: "",
      preferred_skills: "",
      longitude: "",
      city: "",
      cityId: null,
      searchDistanceinKm: 50,
      shareLastSeen: 1,
      whatsAppNotifications: 1,
      status: 1,
      isDeactivate: 0,
      addedBy: userId,
    });

    // Save the user to the database
    const userAdded = await user.save();

    sendWelcomeEmail(emailId, password, userAdded._id);

    return sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "User Added successfully!",
    });
  } else {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "Invalid Parameter",
    });
  }
};

const updateSubAdminUser = async (req, res) => {
  const token = req.headers["x-access-token"];

  if (!token) {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "Unauthorized User",
    });
  }

  const decoded = await verifyToken(token.replace("Bearer ", ""));
  const userId = decoded.user_id;

  const {
    userIdToUpdate,
    first_name,
    last_name,
    emailId,
    password,
    mobileNumber,
    role,
    company,
    designation,
  } = req.body;

  if (!userIdToUpdate) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "User ID to update is required",
    });
  }

  if (!emailId) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "Email is required",
    });
  }

  if (!first_name) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "First Name is required",
    });
  }

  if (!mobileNumber) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "Mobile Number is required",
    });
  }

  const existingUser = await UserModel.findOne({ _id: userIdToUpdate });

  if (!existingUser) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 404,
      msg: "User not found",
    });
  }

  if (emailId && emailId !== existingUser.emailId) {
    const oldUser = await UserModel.findOne({ emailId: emailId });
    if (oldUser) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 200,
        msg: "Email already exists. Please use a different email.",
      });
    }
  }

  // if (mobileNumber && mobileNumber !== existingUser.mobileNumber) {

  //   const oldUserMobile = await UserModel.findOne({
  //     mobileNumber: existingUser.mobileNumber,
  //   });

  //   if (oldUserMobile) {
  //     return await sendResponse(req, res, {
  //       successStatus: false,
  //       statusCode: 200,
  //       msg: "Mobile number aleaready exists. Please use a different mobile number.",
  //     });
  //   }
  // }

  const updatedUserData = {
    first_name,
    last_name,
    emailId,
    mobileNumber,
    role,
    designation,
    company,
  };

  if (password) {
    updatedUserData.password = await bcrypt.hash(password, 10);
  }

  await UserModel.updateOne({ _id: userIdToUpdate }, { $set: updatedUserData });

  await sendWelcomeEmail(emailId, password, userIdToUpdate);

  return sendResponse(req, res, {
    successStatus: true,
    statusCode: 200,
    msg: "User updated successfully!",
  });
};

const deleteSubAdminUser = async (req, res) => {
  const token = req.headers["x-access-token"];

  if (!token) {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "Unauthorized User",
    });
  }

  try {
    const userId = req.params.userId;

    // Delete user from the database
    const deletedUser = await UserModel.findByIdAndDelete(userId);

    if (!deletedUser) {
      return sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "User not found",
      });
    }

    return sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "User Deleted Successfully",
    });
  } catch (error) {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: "Internal Server Error",
    });
  }
};

const adminSubAdminList = async (req, res) => {
  const token = req.headers["x-access-token"];

  if (!token) {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "Unauthorized",
    });
  }

  const decoded = await verifyToken(token.replace("Bearer ", ""));
  const userId = decoded.user_id;

  try {
    const adminUsers = await UserModel.find({ addedBy: userId });

    const usersData = [];

    for (const user of adminUsers) {
      usersData.push({
        id: user._id,
        first_name: user.first_name,
        last_name: user.last_name,
        emailId: user.emailId,
        mobileNumber: user.mobileNumber,
        role: user.role,
        designation: user.designation,
      });
    }

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "All Users List",
      data: { adminUsers, usersData },
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
};

const getSubAdminUserDetails = async (req, res) => {
  try {
    const userId = req.params.userId; // Extract user ID from request parameters

    // Query the database to find user by ID
    const user = await UserModel.findById(userId).select("-password");

    if (!user) {
      return sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "User not found",
      });
    }

    return sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "User Details",
      data: { user },
    });
  } catch (error) {
    // console.error("Error fetching user by ID:", error);
    return res.status(500).json({ message: "Internal Server Error" });
  }
};

const checkingAuthenticated = async (req, res) => {
  await res.status(200).json({
    success: true,
    status: 200,
    msg: "User Token Valid",
  });
};

//Admin - User List
const adminUserList = async (req, res) => {
  try {
    const { user_id } = req.body;

    const superAdmin = UserModel.findById(user_id);

    if (superAdmin !== "" && superAdmin == "superadmin") {
      const data = await UserModel.find(
        { addedBy: user_id },
        { password: false, __v: false }
      );

      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "All Users List",
        data: { data },
      });
    } else {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "Users Not Found",
      });
    }
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
};

//Admin - View User Details
const adminViewUser = async (req, res) => {
  const { user_id, admin_user_id } = req.body;

  try {
    const superAdmin = UserModel.findById(user_id);

    const adminUserId = UserModel.findById(admin_user_id);

    if (
      superAdmin !== "" &&
      superAdmin.role === "superadmin" &&
      adminUserId !== ""
    ) {
      const data = await UserModel.find(
        { _id: adminUserId },
        { addedBy: user_id }
      );

      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "All Users List",
        data: { data },
      });
    } else {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "Users Not Found",
      });
    }
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
};

//Admin - Delete User
const deleteAdminUser = async (req, res) => {
  const token = req.headers["x-access-token"];
  const user_id = req.headers["user_id"];

  if (!token) {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "Unauthorized User",
    });
  }

  try {
    const superAdmin = UserModel.findById(user_id);

    if (superAdmin) {
      const deleteUserId = req.params.userId;

      const dataExist = UserModel.find(
        { _id: deleteUserId },
        { addedBy: superAdmin._id }
      );

      if (dataExist) {
        // Delete user from the database
        const deletedUser = await UserModel.findByIdAndDelete(deleteUserId);

        if (!deletedUser) {
          return sendResponse(req, res, {
            successStatus: false,
            statusCode: 404,
            msg: "User not found",
          });
        }
        return sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "User Deleted Successfully",
        });
      } else {
        return sendResponse(req, res, {
          successStatus: false,
          statusCode: 404,
          msg: "User not found",
        });
      }
    } else {
      return sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "UnAuthenticate User",
      });
    }
  } catch (error) {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: "Internal Server Error",
    });
  }
};

//Admin - Get Requested Points Users List
const getRequestedPointsUser = async (req, res) => {
  const token = req.headers["x-access-token"];
  const user_id = req.headers["user_id"];

  if (!token) {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "Unauthorized User",
    });
  }

  try {
    const superAdmin = UserModel.findById(user_id);

    if (superAdmin) {
      const data = await creditTransactions.find({ superAdminUserId: user_id });

      return sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "All Points Requested Users",
        data: { data },
      });
    } else {
      return sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "Unauthorized User",
      });
    }
  } catch (error) {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: "Internal Server Error",
    });
  }
};

//Admin - Approved Buy Credits to Admin
const approvedRequestedPointsUser = async (req, res) => {
  try {
    const super_admin_user_id = req.body.userId;
    const transactionId = req.body.transactionId;
    const admin_user_id = req.body.admin_use_id;

    if (super_admin_user_id && admin_user_id && transactionId) {

      var user = await UserModel.findOne(
        { _id: super_admin_user_id },
        { __v: false }
      );

      if (!user) {
        return sendResponse(req, res, {
          successStatus: false,
          statusCode: 404,
          msg: "User not found",
        });
      } else {
        const creditsList = await creditTransactions.find({
          _id: transactionId,
          superAdminUserId: super_admin_user_id,
        });

        if (creditsList.approved === "0") {
          await creditTransactions.updateOne(
            { _id: transactionId, superAdminUserId: super_admin_user_id },
            { $set: { approved: "1" } }
          );

          return sendResponse(req, res, {
            successStatus: true,
            statusCode: 200,
            msg: "User Credit Approved",
            data: { creditsList },
          });
        }
      }
    } else {
      return sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "User not found",
      });
    }
  } catch (err) {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: "Internal server error",
    });
  }
};

//Admin - Cancelled Buy Credits to Admin
const declineRequestedPointsUser = async (req, res) => {
  try {
    const super_admin_user_id = req.body.userId;
    const transactionId = req.body.transactionId;
    const admin_user_id = req.body.admin_use_id;

    if (super_admin_user_id && admin_user_id && transactionId) {
      
      var user = await UserModel.findOne(
        { _id: super_admin_user_id },
        { __v: false }
      );

      if (!user) {
        return sendResponse(req, res, {
          successStatus: false,
          statusCode: 404,
          msg: "User not found",
        });
      } else {
        const creditsList = await creditTransactions.find({
          _id: transactionId,
          superAdminUserId: super_admin_user_id,
        });

        if (creditsList.approved === "0" || creditsList.approved === "1") {
          await creditTransactions.updateOne(
            { _id: transactionId, superAdminUserId: super_admin_user_id },
            { $set: { approved: "2" } }
          );

          return sendResponse(req, res, {
            successStatus: true,
            statusCode: 200,
            msg: "User Credit Declined",
            data: { creditsList },
          });
        }
      }
    } else {
      return sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "User not found",
      });
    }
  } catch (err) {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: "Internal server error",
    });
  }
};

const userList = async (req, res) => {
  try {
    const data = await UserModel.find({}, { password: false, __v: false });

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "All Users List",
      data: { data },
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
};

const userDetails = async (req, res) => {
  const userId = req.params.id;

  user = await UserModel.findOne(
    { _id: userId },
    { password: false, __v: false }
  );

  if (user) {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "User Details Found.",
      data: { user },
    });
  } else {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "User not found.",
    });
  }
};

//Allocate Funds to Users by Admin
const allocatedPoints = async (req, res) => {
  const { userId, AllocatedPointsByUserId } = req.body;

  let allocatedPoints = req.body.allocatedPoints;

  allocatedPoints = Number(allocatedPoints);

  // Check if allocatedPoints is not a valid number
  if (allocatedPoints < 0) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "Invalid allocated Points value. Must be a valid number.",
    });
  }

  user = await UserModel.findOne(
    { _id: userId },
    { password: false, __v: false }
  );

  if (!user) {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "User Details Not Found.",
      data: null,
    });
  } else {
    // userId 6673f2b6ab0a22fe06a17434
    // allocated poinrts user id 664c74b79ea7ed04b3b31088

    const adminPointsExist = await userPointModel.findOne({
      userId: AllocatedPointsByUserId,
    });

    if (adminPointsExist.allocatedPoints < allocatedPoints) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "Insufficient Data.",
        data: { updatedUserPoints },
      });
    }

    const subAdminExist = await userPointModel.findOne({ userId: userId });

    if (subAdminExist === null) {
      //Insert Fresh Points
      const data = new userPointModel({
        userId: userId,
        allocatedPoints: allocatedPoints,
        remainPoints: allocatedPoints,
        usedPoints: 0,
        role: "subadmin",
        AllocatedPointsByUserId: AllocatedPointsByUserId,
      });

      const response = await data.save();

      if (response) {
        const updatedUserPoints = await userPointModel.findOneAndUpdate(
          { userId: AllocatedPointsByUserId },
          {
            $inc: {
              allocatedPoints: -allocatedPoints, // Decrement allocatedPoints
              remainPoints: -allocatedPoints, // Decrement remainPoints by allocatedPoints
              usedPoints: allocatedPoints, // Increment usedPoints by allocatedPoints
            },
          },
          { new: true }
        );

        return await sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "User Points Allocated Successfully.",
          data: { updatedUserPoints },
        });
      }
    } else {
      //Update New Points
      const updatedAdminPoints = await userPointModel.findOneAndUpdate(
        { userId },
        {
          $inc: {
            remainPoints: allocatedPoints, // Increment
            allocatedPoints: allocatedPoints, // Increment
          },
        },
        { new: true }
      );

      if (updatedAdminPoints) {
        const updatedUserPoints = await userPointModel.findOneAndUpdate(
          { userId: AllocatedPointsByUserId },
          {
            $inc: {
              allocatedPoints: -allocatedPoints,
              remainPoints: -allocatedPoints, // Decrement remainPoints by allocatedPoints
              usedPoints: allocatedPoints, // Increment usedPoints by allocatedPoints
            },
          },
          { new: true }
        );

        return await sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "User Points Allocated Successfully.",
          data: { updatedUserPoints },
        });
      }
    }
  }
};

//Buy credits fro admin
const buyCredits = async (req, res) => {
  const {
    RequestedAdminUserId,
    superAdminUserId,
    numberOfCredits,
    totalAmount,
    gst,
  } = req.body;

  if (
    !RequestedAdminUserId ||
    !superAdminUserId ||
    !numberOfCredits ||
    !totalAmount ||
    !gst
  ) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 422,
      msg: "Invalid Parameters",
    });
  }

  user = await UserModel.findOne({ _id: RequestedAdminUserId }, { __v: false });

  if (!user) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 404,
      msg: "User Not found",
    });
  } else {
    const data = new creditTransactions({
      RequestedAdminUserId,
      superAdminUserId,
      numberOfCredits,
      totalAmount,
      gst,
    });

    const response = await data.save();

    if (response) {
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 201,
        msg: "Buy credits requested Successfully.",
      });
    }
  }
};

const buyCreditsList = async (req, res) => {
  try {
    const userId = req.body.userId;

    var user = await UserModel.findOne({ _id: userId }, { __v: false });

    if (!user) {
      return sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "User not found",
      });
    } else {
      const creditsList = await creditTransactions.find({
        RequestedAdminUserId: userId,
      });

      console.log("user", creditsList);

      return sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "User Credits List",
        data: { creditsList },
      });
    }
  } catch (err) {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: "Internal server error",
    });
  }
};

//Approved Buy Credit
const approvedCredits = async (req, res) => {
  try {
    const userId = req.body.userId;
    const transactionId = req.body.transactionId;

    var user = await UserModel.findOne({ _id: userId }, { __v: false });

    if (!user) {
      return sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "User not found",
      });
    } else {
      const creditsList = await creditTransactions.find({
        _id: transactionId,
        superAdminUserId: userId,
      });

      if (creditsList.approved === "0") {
        await creditTransactions.updateOne(
          { _id: transactionId, superAdminUserId: userId },
          { $set: { approved: "1" } }
        );

        return sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "User Credit Approved",
          data: { creditsList },
        });
      }
    }
  } catch (err) {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: "Internal server error",
    });
  }
};

//Get User Points
const getUserPoints = async (req, res) => {
  const { userId } = req.body;

  if (!userId) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "User-Id is required.",
      data: null,
    });
  }

  const userPoints = await userPointModel.findOne({
    userId: userId,
  });

  if (userPoints) {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "User Points Details",
      data: { userPoints },
    });
  } else {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 400,
      msg: "User Points Details Not Found",
    });
  }
};

//Add Contact View Points
const addContactViews = async (req, res) => {
  const { userId, ContactUserId, contactView, points } = req.body;

  if (
    userId === "" ||
    ContactUserId === "" ||
    contactView === "" ||
    points === ""
  ) {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 400,
      msg: "Invalid Parameters",
    });
  } else {
    const userContactPoints = await userContactViewModel.find({
      userId: userId,
      ContactUserId: ContactUserId,
    });

    const userPoints = await userPointModel.findOne({ userId: userId });

    if (userContactPoints !== "") {
      if (userPoints.remainPoints > 0) {
        //Update Points
        const updatedSubAdminPoints = await userPointModel.findOneAndUpdate(
          { userId },
          {
            $set: {
              remainPoints: userPoints.remainPoints - 1,
              usedPoints: userPoints.usedPoints + 1,
            },
          },
          { new: true }
        );

        if (updatedSubAdminPoints) {
          const data = new userContactViewModel({
            userId: userId,
            contactUserId: ContactUserId,
            contactView: contactView,
            points: points,
          });

          const response = await data.save();

          if (response) {
            return await sendResponse(req, res, {
              successStatus: true,
              statusCode: 200,
              msg: "User Contact View Point Added Successfully.",
              data: { data },
            });
          }
        } else {
          return await sendResponse(req, res, {
            successStatus: false,
            statusCode: 422,
            msg: "Something went wrong.Please try again later",
          });
        }
      } else {
        return await sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "Insufficient Funds.",
        });
      }
    } else {
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "You are already connected.",
      });
    }
  }
};

//Get Total Contact View points by userID - subadmin
const getTotalContactViews = async (req, res) => {
  const { userId } = req.body;

  if (!userId) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "User Id is required.",
    });
  }

  const userContactViews = await userContactViewModel.find({
    userId: userId,
  });

  if (userContactViews && userContactViews.length > 0) {
    let totalPoints = 0;

    let contactUserData = [];

    await Promise.all(
      userContactViews.map(async (item) => {
        const user = await UserModel.findOne({
          _id: item.contactUserId,
        }).select("-password");

        if (user !== null) {
          const showData = {
            userId: user._id,
            first_name: user.first_name,
            last_name: user.last_name,
            emailId: user.emailId,
            mobileNumber: user.mobileNumber,
            designation: user.designation,
          };
          contactUserData.push(showData);
        }
      })
    );

    userContactViews.forEach((item) => {
      if (item.userId == userId) {
        totalPoints += item.points;
      }
    });

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "User Contact View Details",
      data: { userContactViews, contactUserData, totalPoints },
    });
  } else {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 422,
      msg: "User Contact View Details Not Found",
    });
  }
};

//Check Chat Made Points
const checkChatMadePoints = async (req, res) => {
  const { userId, ContactUserId } = req.body;

  if (userId === "" && ContactUserId === "") {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 400,
      msg: "Invalid Parameters",
      data: null,
    });
  } else {
    const userChatMadePoints = await userChatMadeModel.find({
      userId: userId,
      ContactUserId: ContactUserId,
    });

    if (userChatMadePoints && userChatMadePoints.length > 0) {
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "User Chat Points Details",
        data: { userChatMadePoints },
      });
    }
  }
};

//Check Contact Made Points
const checkContactMadePoints = async (req, res) => {
  const { userId, ContactUserId } = req.body;

  if (userId === "" || ContactUserId === "") {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 400,
      msg: "Invalid Parameters",
    });
  } else {
    
    const userContactDetails = await userContactViewModel.findOne({
      userId: userId,
      contactUserId: ContactUserId,
    });

    if (userContactDetails) {
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "User Contact Points Details",
        data: { userContactDetails },
      });
    } else {
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "User Contact Point Not Found",
      });
    }
  }
};

//Add Chat Made Points
const addChatMade = async (req, res) => {
  const { userId, ContactUserId, chatMade, points } = req.body;

  if (!userId || !ContactUserId || !chatMade || !points) {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 400,
      msg: "Invalid Parameters",
      data: null,
    });
  } else {
    const data = new userChatMadeModel({
      userId: userId,
      contactUserId: ContactUserId,
      chatMade: chatMade,
      points: points,
    });

    const response = await data.save();

    if (response) {
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "User Chat Point Added Successfully.",
        data: { data },
      });
    }
  }
};

//Get Total Chat Points
const getTotalChatMade = async (req, res) => {
  const { userId } = req.body;

  if (!userId) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "User-Id is required.",
    });
  }

  const userChatMade = await userChatMadeModel.find({
    userId: userId,
  });

  if (userChatMade && userChatMade.length > 0) {
    let totalPoints = 0;

    let chatUserData = [];

    await Promise.all(
      userChatMade.map(async (item) => {
        const user = await UserModel.findOne({
          _id: item.contactUserId,
        }).select("-password");

        if (user !== null) {
          const showData = {
            contactUserId: userId,
            first_name: user.first_name,
            last_name: user.last_name,
            emailId: user.emailId,
            mobileNumber: user.mobileNumber,
            userId: user._id,
            designation: user.designation,
          };
          chatUserData.push(showData);
        }
      })
    );

    // Iterate over the data and accumulate points for the given userId
    userChatMade.forEach((item) => {
      if (item.userId == userId) {
        totalPoints += item.points;
      }
    });

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "User Chat Points Details",
      data: { userChatMade, chatUserData, totalPoints },
    });
  } else {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 422,
      msg: "User Chat Point Not Found",
    });
  }
};

//Get all user Points
const getAllUsersConnectPoints = async (req, res) => {
  const { adminId } = req.body;

  if (!adminId) {
    return sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "User Id is required",
    });
  }

  if (adminId) {
    try {
      const adminUsers = await UserModel.find({ addedBy: adminId });

      //Store user points
      const usersWithPoints = [];

      for (const user of adminUsers) {
        const userPoints = await userPointModel.findOne({ userId: user._id });

        // const userContactViews = await userContactViewModel.find({
        //   userId: user._id,
        // });

        // let totalContactPoints = 0;

        // Iterate over the data and accumulate points for the given userId
        // userContactViews.forEach((item) => {
        //   if (item.userId == user._id) {
        //     totalContactPoints += item.points;
        //   }
        // });

        const profileViewed = await profileView.find({
          RequesterId: user._id,
        });

        let profileViewedCount = profileViewed.length;

        // const userChatMade = await userChatMadeModel.find({
        //   userId: user._id,
        // });

        // let totalChatPoints = 0;

        // Iterate over the data and accumulate points for the given userId
        // userChatMade.forEach((item) => {
        //   if (item.userId == user._id) {
        //     totalChatPoints += item.points;
        //   }
        // });

        const totalChat = await chatTrackView.find({
          from_user_id: user._id
        })

        const totalChatCount = totalChat.length;

        const connections = await ConnectionsModel.find({
          from_user_id: user._id
        })

        const connectionsCount = connections.length;

        usersWithPoints.push({
          userId: user._id,
          first_name: user.first_name,
          last_name: user.last_name,
          emailId: user.emailId,
          mobileNumber: user.mobileNumber,
          role: user.role,
          designation: user.designation,
          remained_points: userPoints ? userPoints.remainPoints : 0,
          used_points: userPoints ? userPoints.usedPoints : 0,
          allocated_points: userPoints ? userPoints.allocatedPoints : 0,
          profileViewedCount: profileViewedCount,
          totalChatCount: totalChatCount,
          connectionsCount: connectionsCount
        });
      }

      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "All Users List",
        data: { usersWithPoints },
      });
    } catch (error) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 500,
        msg: error.message,
      });
    }
  }
};

//User Chat Details
const userChatDetails = async (req, res) => {
  const { senderReceiverId } = req.body;

  if (!admin.apps.length) {
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      databaseURL: "https://klout-club-default-rtdb.firebaseio.com",
    });
  }

  const db = admin.database();

  db.ref("/")
    .once("value")
    .then((snapshot) => {
      const data = snapshot.val();
      const filteredData = {};

      Object.keys(data).forEach((key) => {
        const parts = key.split("_");

        const secondPart = parts[1];

        if (key === senderReceiverId) {
          filteredData[senderReceiverId] = data[key];
        }
      });

      return sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "Users Chat Details",
        data: { filteredData },
      });
    })
    .catch((error) => {
      console.error("Error retrieving data:", error);
      res
        .status(500)
        .json({ error: "An error occurred while retrieving data" });
    });
  // console.log("Filtered Database", filteredData);
};

//Sub Admin and User chat Lsit
const subAdminChatUserList = async (req, res) => {
  const { userId } = req.body;

  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: "https://klout-club-default-rtdb.firebaseio.com", // Replace with your Firebase project's database URL
  });

  const db = admin.database();

  db.ref("/")
    .once("value")
    .then((snapshot) => {
      const data = snapshot.val();
      const userData = {};

      Object.keys(data).forEach((key) => {
        const parts = key.split("_");

        const secondPart = parts[1];

        if (secondPart === userId) {
          userData[key] = parts[0];
        }
      });

      return sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "Chat Users List",
        data: { userData },
      });
    })
    .catch((error) => {
      console.error("Error retrieving data:", error);
      res
        .status(500)
        .json({ error: "An error occurred while retrieving data" });
    });
  // console.log("Filtered Database", filteredData);
};



//Save ICP Data
const saveICP = async (req, res) => {
  try {
    const {
      user_id,
      country_id,
      countries,
      companies,
      employeeSize,
      skills,
      industries,
      states,
      jobTitles,
    } = req.body;

    let ICP = await ICPModel.findOne({ user_id, country_id });
    if (!ICP) {
      ICP = new ICPModel({
        user_id,
        country_id,
        countries,
        companies,
        employeeSize,
        skills,
        industries,
        states,
        jobTitles,
      });
    } else {
      if(countries) ICP.countries = countries;
      if (companies) ICP.companies = companies;
      if (employeeSize) ICP.employeeSize = employeeSize;
      if (skills) ICP.skills = skills;
      if (industries) ICP.industries = industries;
      if (states) ICP.states = states;
      if (jobTitles) ICP.jobTitles = jobTitles;
    }
    await ICP.save();
    res.status(201).json(ICP);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

//Get ICP Data
const getICPData = async (req, res) => {
  const { userId } = req.body;

  try {
    const user = await UserModel.findById(userId);

    if (!user) {
      return sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "User not found",
      });
    } else {
      const data = await ICPModel.find({ user_id: userId });

      return sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "Get All ICP Details",
        data: { data },
      });
    }
  } catch (err) {
    return sendResponse(req, res, {
      successStatus: true,
      statusCode: 403,
      msg: err.message,
    });
  }
};

// upload ICP Data

const uploadICP = async (req, res) => {
  const { userId } = req.body;

  try {
    if (!req.files || !req.files.file) {
      return res.status(400).send('Please upload a file');
    }

    const file = req.files.file;
    const path = `./uploads/${Date.now()}-${file.name}`;

    // Move the file to the server
    file.mv(path, async (err) => {
      if (err) {
        return res.status(500).send('Error saving file');
      }

      let data = [];

      // Check file extension to determine parsing method
      if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        // Handle Excel files
        const workbook = xlsx.readFile(path);
        const sheetNames = workbook.SheetNames;
        const sheet = workbook.Sheets[sheetNames[0]];
        data = xlsx.utils.sheet_to_json(sheet);
      } else if (file.name.endsWith('.csv')) {
        // Handle CSV files
        data = [];
        createReadStream(path)
          .pipe(csvParser())
          .on('error', (error) => {
            unlink(path, () => {}); // Clean up the file
            res.status(500).send('Error parsing CSV file');
          })
          .on('data', (row) => {
            data.push(row);
          })
          .on('end', async () => {
            await processDataAndSave(userId, data, res);
          });
        return; // Exit early to avoid processing CSV data twice
      } else {
        // Unsupported file format
        unlink(path, () => {}); // Clean up the file
        return res.status(400).send('Unsupported file format');
      }

      // Process and save data for Excel files
      await processDataAndSave(userId, data, req, res, path);
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: error.message,
    });
  }
};

const processDataAndSave = async (userId, data, req, res, path) => {

  const user = await UploadICPModel.findOne({ userId });

  const parsedData = {
    userId,
    list: []
  };

  data.forEach((row) => {
    if(row.country || row.state || row.skill || row.company || row.industry || row.designation || row.employeeSize || row.priority){
      const eachData = {
        country: row.country ? row.country : "",
        state: row.state ? row.state : "",
        skill: row.skill ? row.skill : "",
        company: row.company ? row.company : "",
        industry: row.industry ? row.industry : "",
        designation: row.designation ? row.designation : "Director, Vice President, VP, Head, Chief Customer Officer, COO, CEO, General Manager, Managing Director, CTO, CMO",
        employeeSize: row.employeeSize ? row.employeeSize : "",
        priority: row.priority ? row.priority : "P4"
      }
      parsedData.list.push(eachData);

    }
  });

  if(!user){
    const uploadICPDocument = new UploadICPModel(parsedData);
    await uploadICPDocument.save();
  }else{
    await UploadICPModel.findOneAndUpdate({ userId }, parsedData);
  }

  // Clean up uploaded file
  unlink(path, (err) => {
    if (err) console.error('Error deleting file:', err);
  });

  return await sendResponse(req, res, {
    successStatus: true,
    statusCode: 200,
    msg: 'Data is uploaded successfully',
  });
};

const getUploadedICPData = async (req, res) => {
  const { userId } = req.body;

  try {
    const user = await UserModel.findById(userId);

    if (!user) {
      return sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "User not found",
      });
    } else {
      const data = await UploadICPModel.find({ userId: userId });

      return sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "Get All ICP Details",
        data: { data },
      });
    }
  } catch (err) {
    return sendResponse(req, res, {
      successStatus: true,
      statusCode: 403,
      msg: err.message,
    });
  }
};


const uploadFile = async(req, res) => {
  if (!req.file || !req.body.userId) {
    return res.status(400).send('File and userId are required');
  }

  

  const userId = req.body.userId;
  const filePath = req.file.path;

  // Parse CSV file and call the controller
  const results = [];
  fs.createReadStream(filePath)
    .pipe(csv())
    .on('data', (data) => results.push(data))
    .on('end', async () => {
      req.body.file = results; // Attach parsed CSV data to req.body
      req.body.userId = userId;
      try {
        await uploadICP(req, res);
        fs.unlinkSync(filePath); // delete the uploaded file
      } catch (error) {
        res.status(500).send('Error saving data to database');
      }
    })
    .on('error', (error) => {
      res.status(500).send('Error parsing CSV file');
    });
}


//Marketing Track
const SendRequestTrackDetails = async (req, res) => {
  try {
    const { request_user_id, receive_user_id } = req.body;

    const data = SendRequestTrackView.find({
      request_user_id: request_user_id,
      receive_user_id: receive_user_id,
    });

    if (data) {
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "All Send Request Data",
        data: { data },
      });
    } else {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "Data Not Found",
      });
    }
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
};

const ProfileViewTrackDetails = async (req, res) => {
  try {
    const { ConnectionId, RequesterId } = req.body;

    const data = profileView.find({
      ConnectionId: ConnectionId,
      RequesterId: RequesterId,
    });

    if (data) {
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "All Profile View Data",
        data: { data },
      });
    } else {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "Data Not Found",
      });
    }
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
};

const ChatTrackDetails = async (req, res) => {
  try {
    const { from_user_id, to_user_id, connectionId } = req.body;

    const data = chatTrackView.find({
      from_user_id: from_user_id,
      to_user_id: to_user_id,
      connectionId: connectionId,
    });

    if (data) {
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "All Chat Data",
        data: { data },
      });
    } else {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "Data Not Found",
      });
    }
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
};

// controller for profileViewDetails
const profileVeiwDetails = async (req, res) =>{
  try{
    const { userId } =  req.body;

    const profileViewData = await profileView.find({
      RequesterId: userId
    })

    const elaboratedProfileViewData = [];

    for(let i=0; i < profileViewData.length; i++){
      const connectionDetails = await UserModel.findById(profileViewData[i].ConnectionId);
      const connectionDetailsObj = connectionDetails.toObject();
      delete connectionDetailsObj.password;
      elaboratedProfileViewData.push(connectionDetailsObj)
    }

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "All Profile View Data",
      data: {profileViewData, elaboratedProfileViewData},
    });

  }catch(error){
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
}

// chat view details 
const chatViewDetails = async (req, res) => {
  try{
    const { userId } = req.body;

    const chatViewData = await chatTrackView.find({
      from_user_id: userId
    });

    const elaborateChatViewData = []

    for(let i=0; i < chatViewData.length; i++){
      const chatDetails = await UserModel.findById(chatViewData[i].to_user_id);
      if(chatDetails){
        const chatDetailsObj = chatDetails.toObject();
        delete chatDetailsObj.password;
        elaborateChatViewData.push(chatDetailsObj)
      }
      
    }

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "All Chat Data",
      data: {chatViewData, elaborateChatViewData},
    });

  }catch(error){
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
}

// find the user with the help of email or phone
const userByEmailOrPhone = async (req, res) => {
  const { emailId, mobileNumber } = req.body;
  try{
    if(emailId && !mobileNumber){
      const user = await UserModel.findOne({ emailId });

      if(user){
        const userObj = user.toObject();
        delete userObj.password
        return await sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "All User",
          data: userObj
        });

      }else{
        return await sendResponse(req, res, {
          successStatus: false,
          statusCode: 400,
          msg: "Invalid Credentials"
        });
      }
    }

    if(!emailId && mobileNumber){
      const user = await UserModel.findOne({ mobileNumber });
      

      if(user){
        const userObj = user.toObject();
        delete userObj.password
        return await sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "All User",
          data: userObj
        });

      }else{
        return await sendResponse(req, res, {
          successStatus: false,
          statusCode: 400,
          msg: "Invalid Credentials"
        });
      }
    }

    if(emailId && mobileNumber){
      const user = await UserModel.findOne({ emailId, mobileNumber });
      

      if(user){
        const userObj = user.toObject();
        delete userObj.password
        return await sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "All User",
          data: userObj
        });

      }else{
        return await sendResponse(req, res, {
          successStatus: false,
          statusCode: 400,
          msg: "Invalid Credentials"
        });
      }
    }

    if(!emailId && !mobileNumber){
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "Please provide email or phone number"
      });
    }

  }catch(error){
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
}

// assign user to subadmin
const assignSubadmin = async (req, res) => {
  const { emailId, adminId } = req.body;
  try{
    if(!emailId || !adminId){
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "Please provide emailid and adminId"
      });
    }

    const user = await UserModel.findOne({ emailId });

    if(user){
      user.role = "subadmin";
      user.addedBy = adminId
      await user.save();

      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "Assigned as subadmin"
      });
    }

  }catch(error){
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
}

// assign subadmin to user
const assignUser = async (req, res) => {
  const { userId } = req.body;
  try{
    if(!userId){
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "Please provide userId"
      });
    }

    const user = await UserModel.findOne({ _id: userId });

    if(user){
      user.role = "user";
      user.addedBy = null
      await user.save();

      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "Assigned as User"
      });
    }

  }catch(error){
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
}

// chat view details 
const connectionsDetail = async (req, res) => {
  try{
    const { userId } = req.body;

    const connectionsData = await ConnectionsModel.find({
      from_user_id: userId
    });

    const elaborateConnectionsData = []

    for(let i=0; i < connectionsData.length; i++){
      const connectionsDetail = await UserModel.findById(connectionsData[i].to_user_id);
      if(connectionsDetail){
        const connectionsDetailObj = connectionsDetail.toObject();
        delete connectionsDetailObj.password;
        elaborateConnectionsData.push(connectionsDetailObj)
      }
      
    }

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "All Connections Data",
      data: {connectionsData, elaborateConnectionsData},
    });

  }catch(error){
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
}

module.exports = {
  assignUser,
  uploadFile,
  uploadICP,
  getICPData,
  connectionsDetail,
  assignSubadmin,
  userByEmailOrPhone,
  chatViewDetails,
  profileVeiwDetails,
  saveICP,
  adminUserList,
  test,
  register,
  login,
  uploadUsers,
  userList,
  userDetails,
  allocatedPoints,
  getUserPoints,
  getTotalContactViews,
  addContactViews,
  getTotalChatMade,
  addChatMade,
  adminSubAdminList,
  addSubAdmin,
  getSubAdminUserDetails,
  updateSubAdminUser,
  deleteSubAdminUser,
  getAllUsersConnectPoints,
  userChatDetails,
  subAdminChatUserList,
  checkChatMadePoints,
  checkContactMadePoints,
  checkingAuthenticated,
  userView,
  sendInterakartMessage,
  logout,
  buyCredits,
  buyCreditsList,
  approvedCredits,
  adminViewUser,
  deleteAdminUser,
  getRequestedPointsUser,
  approvedRequestedPointsUser,
  approvedCredits,
  ChatTrackDetails,
  ProfileViewTrackDetails,
  SendRequestTrackDetails,
  getUploadedICPData
};
