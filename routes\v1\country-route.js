const express = require('express');
const router = express.Router();
const CountryController = require('../../controllers/v1/country-controller');
// const auth = require("../../middleware/auth");
// const {sendResponse} = require("../../utils/utility");

//Post Method
router.post('/add', async (req, res) => {
    const exist = await CountryController.create(req, res);
})
//Get all Method
router.get('/getAll', async (req, res) => {
    const exist = await CountryController.getAll(req, res);
})
//find one  Method
router.get('/findone', async (req, res) => { 
    const exist = await CountryController.details(req, res);
})
// Search 
router.get('/search', async (req, res) => { 
    const exist = await CountryController.search(req, res);
})

module.exports = router;