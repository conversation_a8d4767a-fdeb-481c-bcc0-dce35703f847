const mongoose = require("mongoose");

const { Schema } = mongoose;

const ChatTrackViewSchema = new mongoose.Schema(
  {
    from_user_id: {
      required: true,
      type: Schema.Types.ObjectId,
      ref: "user",
    },
    to_user_id: {
      required: true,
      type: Schema.Types.ObjectId,
      ref: "user",
    },
    connectionId: {
      required: true,
      type: String,
    },
    chatStatus: {
      required: true,
      type: String,
    },
    ModifiedDate: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
  }
);

module.exports = mongoose.model("chat_track_view", ChatTrackViewSchema);
