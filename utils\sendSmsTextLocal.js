const axios = require('axios');

const apiKey = process.env.TEXTLOCAL_API_KEY;
const sender = 'INSKLT';

const textLocalService = async (mobileNumber, otp) => {
    const content = `${otp} is your OTP for verifying your profile with KloutClub by Insightner Marketing Services`;

    var msg = encodeURIComponent(content);

    var data =
      "apikey=" +
      apiKey +
      "&sender=" +
      sender +
      "&numbers=" +
      mobileNumber +
      "&message=" +
      msg;

  try {
    const response = await axios.post(`https://api.textlocal.in/send?${data}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    throw new Error('Error sending template message');
  }
};

module.exports = textLocalService;
