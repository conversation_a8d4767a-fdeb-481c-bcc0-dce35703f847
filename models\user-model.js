const mongoose = require("mongoose");
const { Schema } = mongoose;

const userSchema = new mongoose.Schema(
  {
    first_name: {
      required: true,
      type: String,
    },
    last_name: {
      required: false,
      type: String,
    },
    emailId: {
      required: true,
      // unique: true,
      type: String,
    },
    mobileNumber: {
      type: Number,
    },
    profileImage: {
      type: String,
    },
    company: {
      type: String,
    },
    userEnteredCompany: {
      type: String
    },
    designation: {
      type: String,
    },
    userEnteredDesignation: {
      type: String
    },
    industryId: { type: Schema.Types.ObjectId, ref: "industry" },
    location: {
      type: String,
    },
    linkedInId: {
      type: String,
    },
    linkedInAccessToken: {
      type: String,
    },
    deviceToken: {
      type: String,
    },
    deviceVersion: {
      type: String,
    },
    appVersion: {
      type: String,
    },
    deviceType: {
      type: String,
    },
    deviceName: {
      type: String,
    },
    latitude: {
      type: String,
    },
    longitude: {
      type: String,
    },
    city: {
      type: String,
    },
    cityId: {
      // type: { type: Schema.Types.ObjectId, ref: 'cities' },
      type: { type: String, ref: "cities" },
    },
    status: {
      type: String,
      required: false,
    },
    password: {
      required: false,
      type: String,
    },
    whatsAppNotifications: {
      required: false,
      type: String,
    },
    shareLastSeen: {
      required: false,
      type: String,
    },
    searchDistanceinKm: {
      required: false,
      type: String,
    },
    industry: {
      required: false,
      type: String,
    },
    skills: {
      required: false,
      type: String,
    },
    preferred_skills: {
      required: false,
      type: String,
    },
    aboutMe: {
      required: false,
      type: String,
    },
    industryName: {
      required: false,
      type: String,
    },
    professionalHighlight: {
      required: false,
      type: String,
    },
    awards: {
      required: false,
      type: String,
    },
    featured: {
      required: false,
      type: String,
    },
    education: {
      required: false,
      type: String,
    },
    linkedInId: {
      required: false,
      type: String,
    },
    googleId: {
      required: false,
      type: String,
    },
    appleId: {
      required: false,
      type: String,
    },
    images: {
      required: false,
      type: String,
    },
    reason: {
      required: false,
      type: String,
    },
    feedback: {
      required: false,
      type: String,
    },
    role: {
      required: false, //superadmin, admin, subadmin, user
      type: String,
    },
    isDeactivate: {
      type: Number,
    },
    addedBy: {
      type: Schema.Types.ObjectId,
      ref: "user",
    },
    blocked: [Schema.Types.ObjectId],
    blockedBy: [Schema.Types.ObjectId],
    experience: String,
    jobFunction: String,
    isOldUser: {
      type: Boolean,
      default: false
    },
    showEmail: {
      type: Boolean,
      default: true
    },
    showMobile: {
      type: Boolean,
      default: true
    },
    planExpiryDays: {
      type: Number,
      default: 0
    },
    linkedinProfileUrl: {
      type: String,
      required: false
    },
    linkedinFollowers:{
      type: Number,
      default: 0
    },
    showProfileImage: {
      type: Boolean,
      default: true
    },
    showProfileImageToConnections: {
      type: Boolean,
      default: true
    },
    xProfileUrl: {
      type: String,
      required: false
    },
    xFollowers:{
      type: Number,
      default: 0
    },
    lastDecrementDate:{
      type: Date
    }
  },
  {
    timestamps: {
      createdAt: "createdAt", // Use `created_at` to store the created date
      updatedAt: "updatedAt", // and `updated_at` to store the last updated date
    },
  }
);

module.exports = mongoose.model("user", userSchema);
