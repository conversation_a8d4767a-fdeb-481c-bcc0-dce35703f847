const express = require('express');
const router = express.Router();
//const auth = require("../middleware/auth");
const {sendResponse} = require("../../utils/utility");

var AWS =  require('aws-sdk');
//const upload = multer({dest: "uploads/"})
//Post Method

router.post('/image', async (req, res) => {

    AWS.config.update({
        accessKeyId:"********************",
        secretAccessKey: "5i1tN1UG1X4tVxNixZcCeuCGob6FOyVKS/TVCXtR",
    })

    const s3 = new AWS.S3();

    const fileContent = Buffer.from(req.files.image.data, 'binary');

    const params = {
        Bucket : "dev-url/klout/uploads/users/profile_images/",
        Key: req.files.image.name,
        Body: fileContent,
    }

    s3.upload(params, (err, data)=>{
        if(err){
            throw err;
        }
        res.send({
            status: 200,
            success: true,
            data
        })
    });
})

module.exports = router;