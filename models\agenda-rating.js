const mongoose = require('mongoose');

const agendaRating = new mongoose.Schema({
    eventUuid: {
        type: String,
        required: [true, 'Event uuid is required']
    },
    eventTitle: {
        type: String,
        required: [true, 'Event title is required']
    },
    agendaTitle: {
        type: String,
        required: [true, 'Agenda title is required']
    },
    agendaUuid: {
        type: String,
        required: [true, 'Agenda uuid is required']
    },
    givenBy: {
        type: String,
        required: [true, 'Given by is required']
    },
    givenTo: {
        type: Number,
        required: [true, 'Given to is required']
    },
    rating: {
        type: Number,
        required: [true, 'Rating is required']
    }
}, { timestamps: true });

module.exports = mongoose.model('agenda_rating', agendaRating);