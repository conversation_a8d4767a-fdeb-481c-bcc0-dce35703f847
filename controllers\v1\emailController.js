const schedule = require('node-schedule');
const emailService = require('../../services/emailService');
const NotificationModel = require('../../models/notifications');
const UserModel = require('../../models/user-model');
const UnsubscribeModel = require('../../models/unsubscribe');

function isValidEmail(email) {
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
}

async function profileViewEmail() {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    const unseenProfileViewNotification = await NotificationModel.find({
        isRead: 0,
        type: 3,
        createdAt: {
            $lt: oneWeekAgo
        }
    });

    const grouping = Object.values(
        unseenProfileViewNotification.reduce((acc, notification) => {
            const userId = notification.user_id;

            if (!acc[userId]) {
                acc[userId] = [];
            }

            acc[userId].push(notification);

            return acc;
        }, {})
    );

    for (let i = 0; i < grouping.length; i++) {
        const eachLength = grouping[i].length;
        let nextArrayUptoLength;
        // if (eachLength < 3) {
        //     nextArrayUptoLength = eachLength;
        // }

        const userDetail = await UserModel.findOne({ _id: grouping[i][0].user_id });
        if (userDetail) {
            const senderEmail = userDetail.emailId;
            // if(userDetail.first_name == "Klout"){
            //     console.log(userDetail)
            // }
            const nameArray = [];
            const companyArray = [];
            const designationArray = []
            // Remove duplicates from grouping[i] based on from_user_id
            const uniqueGrouping = grouping[i].filter((item, index, self) =>
                index === self.findIndex((t) => t.from_user_id === item.from_user_id)
            );

            for (let j = 0; j < uniqueGrouping.length; j++) {
                const fromUserDetail = await UserModel.findOne({ _id: uniqueGrouping[j].from_user_id });

                if (fromUserDetail) {
                    // Skip if any required field is missing
                    if (!fromUserDetail.first_name || !fromUserDetail.company || !fromUserDetail.designation) {
                        continue;
                    }

                    // Exit if we already have 3 items in each array
                    if (nameArray.length >= 3 && companyArray.length >= 3 && designationArray.length >= 3) {
                        break;
                    }

                    // Add details to respective arrays
                    const name = fromUserDetail.first_name + ' ' + fromUserDetail.last_name;
                    const company = fromUserDetail.company;
                    const designation = fromUserDetail.designation;

                    nameArray.push(name);
                    companyArray.push(company);
                    designationArray.push(designation);
                }
            }



            // console.log


            if (nameArray.length > 0 || companyArray.length > 0 || designationArray.length > 0) {
                // if (senderEmail === "<EMAIL>") {
                const variables = {
                    name: userDetail.first_name + ' ' + userDetail.last_name,
                    viewerNameOne: nameArray[0],
                    viewerNameTwo: nameArray[1],
                    viewerNameThree: nameArray[2],
                    viewerCompanyOne: companyArray[0],
                    viewerCompanyTwo: companyArray[1],
                    viewerCompanyThree: companyArray[2],
                    viewerDesignationOne: designationArray[0],
                    viewerDesignationTwo: designationArray[1],
                    viewerDesignationThree: designationArray[2],
                    pendingNotificationCount: eachLength,
                    email: senderEmail
                }
                const checkEmail = await UnsubscribeModel.findOne({email: senderEmail});
                const validEmail = isValidEmail(senderEmail);
                if(!checkEmail && validEmail){
                    await emailService.sendTemplateEmail(
                        senderEmail,
                        'Your profile is being viewed - Review visitors.',
                        'profileView',
                        variables
                    );
                    console.log('email send successfully')
                }
                
            }

            // }

        }

    }


}





async function connectionRequestEmail (requesterUserId, recieverUserId) {

    console.log("hello");

    schedule.scheduleJob(new Date(Date.now() + 60 * 60 * 1000 ), async function () {

    const pendingUserNotification = await NotificationModel.findOne({ user_id: recieverUserId, from_user_id: requesterUserId, type: 1 });

    if(pendingUserNotification){

        console.log(pendingUserNotification.isRead)

        if(!pendingUserNotification.isRead){
            const isValidReciever = await UserModel.findOne({ _id: recieverUserId });
            const isValidSender = await UserModel.findOne({ _id: requesterUserId })

            if(isValidReciever && isValidSender) {
                const recieverEmail = isValidReciever.emailId;
                const recieverName = isValidReciever.first_name + ' ' + isValidReciever.last_name;
                const senderName = isValidSender.first_name + ' ' + isValidSender.last_name;
                const imageBaseUrl = 'https://klout-image.s3.ap-south-1.amazonaws.com/';
                const senderJobTitle = isValidSender.designation;
                const senderLocation = isValidSender.city;
                const senderImage = isValidSender.profileImage;

                const nearByProfiles = await UserModel.aggregate([
                    { $match: { city: isValidReciever.city } },
                    { $sample: { size: 5 } }

                ])


                const variables = {
                    name: recieverName,
                    email: recieverEmail,
                    photoBaseUrl: imageBaseUrl,
                    senderImagePath: senderImage,
                    senderName: senderName,
                    senderJobTitle: senderJobTitle,
                    senderLocation: senderLocation,
                    nearbyOneImagePath : nearByProfiles[0].profileImage,
                    nearbyOneName: nearByProfiles[0].first_name + ' ' + nearByProfiles[0].last_name,
                    nearbyOneJobTtile: nearByProfiles[0].designation,
                    nearbyTwoImagePath: nearByProfiles[1].profileImage,
                    nearbyTwoName: nearByProfiles[1].first_name + ' ' + nearByProfiles[1].last_name,
                    nearbyTwoJobTtile: nearByProfiles[1].designation,
                    nearbyThreeImagePath: nearByProfiles[2].profileImage,
                    nearbyThreeName: nearByProfiles[2].first_name + ' ' + nearByProfiles[2].last_name,
                    nearbyThreeJobTtile: nearByProfiles[2].designation,
                    nearbyFourImagePath: nearByProfiles[3].profileImage,
                    nearbyFourName: nearByProfiles[3].first_name + ' ' + nearByProfiles[3].last_name,
                    nearbyFourJobTtile: nearByProfiles[3].designation,
                    nearbyFiveImagePath: nearByProfiles[4].profileImage,
                    nearbyFiveName: nearByProfiles[4].first_name + ' ' + nearByProfiles[4].last_name,
                    nearbyFiveJobTtile: nearByProfiles[4].designation,
                }


                const checkEmail = await UnsubscribeModel.findOne({email: recieverEmail});



                const validEmail = isValidEmail(recieverEmail);

                if(!checkEmail && validEmail) {

                    await emailService.sendTemplateEmail(
                        recieverEmail,
                        // '<EMAIL>',
                        `View my connection request, ${recieverName}`,
                        'connectionRequest',
                        variables
                    );

                    console.log('connection email send successfully')

                }


            }
        }

    }

    });

}


// profileViewEmail();
// Schedule the job to run every Tuesday at 7:35 PM
schedule.scheduleJob({ dayOfWeek: 2, hour: 21, minute: 18 }, async () => {
    console.log('Running profileViewEmail job...');
    await profileViewEmail();
});


module.exports = {
    connectionRequestEmail
}

