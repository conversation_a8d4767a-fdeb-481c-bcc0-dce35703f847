const mongoose = require("mongoose");

const { Schema } = mongoose;

const SendRequestTrackViewSchema = new mongoose.Schema(
  {
    request_user_id: {
      required: true,
      type: Schema.Types.ObjectId,
      ref: "user",
    },
    receive_user_id: {
      required: true,
      type: Schema.Types.ObjectId,
      ref: "user",
    },
    request: {
      required: true,
      type: String,
    },
    ModifiedDate: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
  }
);

module.exports = mongoose.model("send_request_track_view", SendRequestTrackViewSchema);
