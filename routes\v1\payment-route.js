const express = require('express');
const paymentController = require('../../controllers/v1/paymentController');

const router = express.Router();

router.post('/basic-plan', paymentController.purchaseBasicPlan)
    .post('/capture-payment-screen', paymentController.captureUserOnPaymentScreen)
    .post('/list-capture-payment-screen', paymentController.listAllUserClickOnPaymentScreen)
    .post('/purchase-event-plan', paymentController.purchaseEventPlan)
    .post('/get-payment', paymentController.getPayment)
    .post('/verify/:txnid', paymentController.verifyPayment)
    .post('/embed-get-payment', paymentController.getPaymentEmbed)
    .post('/embed-verify/:txnid', paymentController.verifyPaymentEmbed)
    .post('/wallet-topup',paymentController.walletTopUp)
    .post('/verify-wallet-payment/:txnid/:uuid/:token',paymentController.verifyWalletPayment);


module.exports = router;