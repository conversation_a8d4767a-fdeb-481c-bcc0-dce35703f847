const IndustryModel = require('../models/industry-model');

const findbyIndustryName = async (industryName) => {
    return await IndustryModel.findOne({ industryName });
};

const insertIndustryData = async (req) => {
    
    const data = new IndustryModel({
        industryName: req.body.industryName,
        status: req.body.status
    })

    return await data.save();
};

const findAll = async () => {
    return await IndustryModel.find();
};
  
module.exports = {
    findbyIndustryName,
    insertIndustryData,
    findAll
};