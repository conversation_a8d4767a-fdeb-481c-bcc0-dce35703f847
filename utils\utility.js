const UserModel = require('../models/user-model');
const axios = require('axios');
const { OpenAI } = require("openai");

const openai = new OpenAI({
    apiKey: process.env.OPENAI_KEY
});


async function generateAboutMe(designation, company, basePrompt) {
  const prompt = basePrompt.replace("[Your Designation]", designation || "Professional")
                           .replace("[Your Company]", company || "a reputable organization");

  try {
    // Send request to OpenAI API
    const response = await openai.chat.completions.create({
        model: "gpt-4o", // Use gpt-4o or gpt-3.5-turbo
        messages: [{ role: "system", content: prompt }],
        temperature: 0.7,
        max_tokens: 1000,
      });
  
      // Clean and parse the JSON response
    //   const cleanResponse = cleanJsonResponse(response.choices[0].message.content);
    //   return cleanResponse;
    return response.choices[0].message.content.trim();
  } catch (error) {
    console.error("Error generating about me:", error.message);
    return null;
  }
}

const sendResponse = async (req, res, response) => {
  const { successStatus, statusCode, msg, data } = response;
  const success = successStatus;
  return res.status(statusCode).send({ status: success, message: msg, result: data })
};

const distanceBt2Points = (lat1, lon1, lat2, lon2) => {

  function toRad(Value) {
    return Value * Math.PI / 180;
  }
  // convert from degrees to radians
  var R = 6371; // km
  
  var dLat = toRad(lat2 - lat1);
  var dLon = toRad(lon2 - lon1);
  var lat1 = toRad(lat1);
  var lat2 = toRad(lat2);

  var a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.sin(dLon / 2) * Math.sin(dLon / 2) * Math.cos(lat1) * Math.cos(lat2);
  var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  var d = R * c; 
  return d;
};

const removeWhiteSpaceFromNumber = (number) => {
  return String(number).replace(/\s+/g, "");
}

function trimToTenDigits(phone) {
  const digitsOnly = phone.replace(/\D/g, '');

  return digitsOnly.slice(-10);
}



async function addAboutUs(mobile){
  const prompt = `I want you to act as a professional bio writer. Write a crisp, engaging, and credible 'About Me' section for my personal profile. I am the [Your Designation] at [Your Company]. Highlight my role, what I am responsible for, my passion for the work, and any value I bring to clients or the industry. Keep it warm but professional, suitable for LinkedIn, websites, and event profiles. The tone should be confident but not boastful, and the length should be around 3-5 sentences. Optionally, you can add a closing line about what drives me or my vision for the future.`;
  const user = await UserModel.findOne({ mobileNumber: mobile });
  const designation = user.designation;
  const company = user.company;

  const aboutMe = await generateAboutMe(designation, company, prompt);
  user.aboutMe = aboutMe;

  await user.save();

}


module.exports = {
  sendResponse,
  distanceBt2Points,
  removeWhiteSpaceFromNumber,
  trimToTenDigits,
  addAboutUs
}; 