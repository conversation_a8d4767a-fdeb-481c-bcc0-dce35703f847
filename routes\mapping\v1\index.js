const express = require("express");
const companyMasterRoute = require("./companyMaster");
const designationMasterRoute = require("./designationMaster");
const premiumDataRoute = require("./premiumData");
const notificationReportRoute = require("./notificationReport");
const peopleRoute = require('./people');


const router = express.Router();

router.use("/company-master", companyMasterRoute);
router.use("/designation-master", designationMasterRoute);
router.use("/premium-data", premiumDataRoute);
router.use("/notification-report", notificationReportRoute);
router.use("/people", peopleRoute);

module.exports = router;
