const mongoose = require('mongoose');

const lastAttendedEventSchema = new mongoose.Schema({
    mobileNumber: {
        type: Number,
        required: true
    },
    eventUuid: {
        type: String,
        required: true
    }

}, {
    timestamps: {
        createdAt: 'createdAt', // Use `created_at` to store the created date
        updatedAt: 'updatedAt' // and `updated_at` to store the last updated date
    }
})

module.exports = mongoose.model('lastAttendedEvent', lastAttendedEventSchema)