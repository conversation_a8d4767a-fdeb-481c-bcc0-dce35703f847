const mongoose = require('mongoose');
const { Schema } = mongoose;

const userSchema = new mongoose.Schema({
    user_id: {
        required: true,
        type: String
    },
    name: {
        required: true,
        type: String
    },
    emailId: {
        required: true,
        unique: true,
        type: String
    },
    mobileNumber: {
        type: Number
    },
    profileImage: {
        type: String
    },
    company: {
        type: String
    },
    designation: {
        type: String
    },
    industryId: { type: Schema.Types.ObjectId, ref: 'industry' },
    location: {
        type: String
    },
    linkedInId: {
        type: String
    },
    linkedInAccessToken: {
        type: String
    },
    deviceToken: {
        type: String
    },
    deviceVersion: {
        type: String
    },
    appVersion: {
        type: String
    },
    deviceType: {
        type: String
    },
    deviceName: {
        type: String
    },
    latitude: {
        type: String
    },
    longitude: {
        type: String
    },
    city: {
        type: String
    },
    cityId: {
        type: { type: Schema.Types.ObjectId, ref: 'cities' },
    },
    status: {
        type: String,
        required: false,
    },
    password: {
        required: false,
        type: String
    },
    whatsAppNotifications: {
        required: false,
        type: String
    },
    shareLastSeen: {
        required: false,
        type: String
    },
    searchDistanceinKm: {
        required: false,
        type: String
    },
    industry: {
        required: false,
        type: String
    },
    skills: {
        required: false,
        type: String
    },
    aboutMe: {
        required: false,
        type: String
    },
    industryName: {
        required: false,
        type: String
    },
    professionalHighlight: {
        required: false,
        type: String
    },
    awards: {
        required: false,
        type: String
    },
    featured: {
        required: false,
        type: String
    },
    linkedInId: {
        required: true,
        type: String
    },
    images: {
        required: false,
        type: String
    },
    reason: {
        required: false,
        type: String
    },
    feedback: {
        required: false,
        type: String
    },
}, {
    timestamps: {
        createdAt: 'createdAt', // Use `created_at` to store the created date
        updatedAt: 'updatedAt' // and `updated_at` to store the last updated date
    }
})

module.exports = mongoose.model('deleted_user', userSchema)