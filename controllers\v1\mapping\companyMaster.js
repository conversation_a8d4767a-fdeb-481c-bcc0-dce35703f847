const CompanyMasterModel = require('../../../models/companyMaster');
const UnmappedCompanyModel = require('../../../models/unMappedCompany');
const UserModel = require('../../../models/user-model');
const UnmappedOrNewCompanyModel = require('../../../models/unmappedOrNewCompany');



// created add company to the company master 
exports.addCompany = async(req, res) => {
    const { profileUrl, company, website , industry, companySize, headquarters, specialities, overview, mappedTo, companyLogo } = req.body;
    try{
        const existingCompany = await CompanyMasterModel.findOne({ company: company.toLowerCase() });

        if(existingCompany){
            return res.status(404).json({
                status: false,
                message: 'Company is already Added'
            })
        }
        const data = new CompanyMasterModel( { profileUrl, company: company.toLowerCase(), website , industry, companySize, headquarters, specialities, overview, mappedTo, companyLogo } );
        const isSaveCompany = await data.save();

        if(!isSaveCompany){
            return res.status(404).json({
                status: false,
                message: 'Something Wrong'
            })
        }

        await UnmappedOrNewCompanyModel.findOneAndDelete({ company: company.toLowerCase() });

        return res.status(201).json({
            status: true,
            message: 'Company is added to the list'
        })

    }catch(error){
        return res.status(400).json({
            status: false,
            message: error.message
        })
    }
}

//update the company in the company master
// exports.updateCompany = async(req, res) => {
//     const { id } = req.params;
//     const updatedData = req.body;

//     try{
//         const update = await CompanyMasterModel.findByIdAndUpdate(id, updatedData, {
//             new: true
//         });

//         if(!update){
//             return res.status(404).json({
//                 status: false,
//                 message: 'Company not found'
//             });
//         }

//         return res.status(200).json({
//             status: true,
//             data: update,
//             message: 'Data is updated successfully'
//         });

//     }catch(error){

//         return res.status(404).json({
//             status: true,
//             message: error.message
//         })
//     }

// }

exports.updateCompany = async (req, res) => {
    const { id } = req.params;
    const updatedData = req.body;

    try {
        // Convert company name to lowercase if provided
        if (updatedData.company) {
            updatedData.company = updatedData.company.toLowerCase();
        }

        // Find the existing company data
        const previousData = await CompanyMasterModel.findById(id);
        if (!previousData) {
            return res.status(404).json({
                status: false,
                message: "Company not found",
            });
        }

        const previousCompany = previousData.company;

        // Update company data
        const update = await CompanyMasterModel.findByIdAndUpdate(id, updatedData, {
            new: true,
        });

        if (!update) {
            return res.status(404).json({
                status: false,
                message: "Company not found",
            });
        }

        res.status(200).json({
            status: true,
            data: update,
            message: "Data is updated successfully",
        });

        // If company name was updated, update users with the previous company name
        if (updatedData.company && updatedData.company !== previousCompany) {
            await UserModel.updateMany(
                { company: previousCompany },
                { $set: { company: updatedData.company } }
            );
        }

    } catch (error) {
        return res.status(500).json({
            status: false,
            message: error.message,
        });
    }
};


// show the company in company masters
exports.viewCompany = async(req, res) => {
    const { id } = req.params;

    try{
        const company = await CompanyMasterModel.findById(id);

        if(!company){
            return res.status(400).json({
                status: false,
                message: 'Company not found',
            })
        }

        return res.status(200).json({
            status: true,
            data: company,
            message: "View Company"
        });
    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message,
        })
    }
}

//delete the comapany
exports.deleteCompany = async (req, res) => {
    const { id } = req.params;
    
    try{
        const deleteCompany = await CompanyMasterModel.findByIdAndDelete(id);

        if(!deleteCompany){
            return res.status(404).json({
                status: false,
                message: "Company not found"
            });
        }

        return res.status(200).json({
            status: true,
            message: "Company is deleted successfully"
        });


    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message
        })
    }
}

// exports.getAllCompany = async (req, res) => {
//     console.log(req.query)
//     const page = parseInt(req.query.page) || 1;
//     const search = req.query.search || "";
//     const limit = 50;
//     const skip = (page - 1) * limit;

//     try{
//         const query = search ? { company: { $regex: search, $options: 'i' } } : {};

//         const companies = await CompanyMasterModel
//                                                 .find(query)
//                                                 .limit(limit)
//                                                 .skip(skip)
//                                                 .select('company industry companySize mappedTo website specialities overview headquarters companyLogo');
        
//         const totalCompanies = await CompanyMasterModel.countDocuments();

//         return res.status(200).json({
//             status: true,
//             data: { companies, totalCompanies },
//             message: 'All Companies'
//         })


//     }catch(error){

//         return res.status(404).json({
//             status: false,
//             message: error.message
//         })

//     }






// }

exports.getAllCompany = async (req, res) => {

    const page = parseInt(req.query.page) || 1;
    const search = req.query.search || "";
    const industry = req.query.industry || "";
    const employeeSize = req.query.employeeSize || "";
    const logo = req.query.logo || ""; // Can be "true" or "false"

    const limit = 50;
    const skip = (page - 1) * limit;

    try {
        // Build dynamic query based on search, industry, employeeSize, and logo
        const query = {};

        // Search by company name (case-insensitive)
        if (search) {
            query.company = { $regex: search, $options: "i" };
        }

        // Filter by industry if provided
        if (industry) {
            query.industry = { $regex: industry, $options: "i" };
        }

        // Filter by employee size if provided
        if (employeeSize) {
            query.companySize = { $regex: employeeSize, $options: "i" };
        }

        // Filter by logo status (empty or not empty)
        if (logo === "false") {
            query.companyLogo = "";
        } else if (logo === "true") {
            query.companyLogo = { $ne: "" }; // Not empty
        }

        // Fetch companies matching the query
        const companies = await CompanyMasterModel
            .find(query)
            .limit(limit)
            .skip(skip)
            .select("company industry companySize mappedTo website specialities overview headquarters companyLogo news");

        // Count total matching companies
        const totalCompanies = await CompanyMasterModel.countDocuments(query);

        // Return success response
        return res.status(200).json({
            status: true,
            data: { companies, totalCompanies },
            message: "All Companies",
        });
    } catch (error) {
        // Return error response
        return res.status(404).json({
            status: false,
            message: error.message,
        });
    }
};

exports.addUnmappedCompany = async (req, res) => {
    const { company, isMapped, mappedWith } = req.body;
    try{
        const isExisting = await UnmappedCompanyModel.findOne({ company });
        if(isExisting){
            const isExistingMapped = await UnmappedCompanyModel.findOne({ company, isMapped: true});
            if(isExistingMapped){

                await UserModel.updateMany(
                    { company: company },
                    { $set: { company: isExisting.mappedWith } }
                );

                return res.status(200).json({
                    status: true,
                    message: 'Company is already Existed'
                })
            }

            return res.status(200).json({
                status: true,
                message: 'Company is already Existed'
            })
        }

        const newCompany = new UnmappedCompanyModel({ company, isMapped, mappedWith });
        await newCompany.save();

        await UnmappedOrNewCompanyModel.findOneAndDelete({ company });

        return res.status(201).json({
            status: true,
            message: 'Company is added successfully'
        })
    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message
        })
    }
}

exports.mappingUnmappedCompany = async(req, res) => {
    const { unmappedCompany, mappedCompany } = req.body

    try{
        const searchMappedCompany = await CompanyMasterModel.findOne({ company: mappedCompany });
        const searchUnmappedCompany = await UnmappedCompanyModel.findOne({ company: unmappedCompany });
        console.log(searchUnmappedCompany)

        searchMappedCompany.mappedTo = [...searchMappedCompany.mappedTo , unmappedCompany];
        await searchMappedCompany.save();

        searchUnmappedCompany.isMapped = true;
        searchUnmappedCompany.mappedWith = mappedCompany;
        await searchUnmappedCompany.save();

        await UserModel.updateMany(
            { company: unmappedCompany },
            { $set: { company: mappedCompany } }
        );

        return res.status(200).json({
            status: true,
            message: 'Company is Successfully Mapped'
        })


    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message
        })
    }

}

// this code is responsible for unmapped the map companies which is mapped
exports.unmappedMappedCompany = async (req, res) => {
    const { company, mappedWith } = req.body;

    try {
        if (!company || !mappedWith) {
            return res.status(400).json({
                status: false,
                message: 'Please provide both company and mappedWith fields'
            });
        }

        const existedMappedCompany = await UnmappedCompanyModel.findOne({ company, mappedWith, isMapped: true });

        if (!existedMappedCompany) {
            return res.status(404).json({
                status: false,
                message: 'Company not found'
            });
        }

        // Update the unmapped company
        existedMappedCompany.mappedWith = "";
        existedMappedCompany.isMapped = false;
        await existedMappedCompany.save();

        await CompanyMasterModel.updateMany(
            { company: mappedWith },
            { $pull: { mappedTo: company } }
        );

        // Update company field in UserModel with userEnteredCompany
        await UserModel.updateMany(
            { company: mappedWith },
            [{ $set: { company: "$userEnteredCompany" } }]
        );

        return res.status(200).json({
            status: true,
            message: 'Company unmapped successfully'
        });

    } catch (error) {
        return res.status(500).json({
            status: false,
            message: error.message
        });
    }
};

exports.getUnmappedCompany = async ( req, res) => {
    try{
        const allUnmappedComapany = await UnmappedCompanyModel.find();

        return res.status(200).json({
            status: true,
            data: allUnmappedComapany,
            message: 'All Unmapped Companies'
        })
    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message
        })
    }
}

exports.deleteUnmappedCompany = async (req, res) =>{
    const id = req.params.id;
    try{
        const company = await UnmappedCompanyModel.findByIdAndDelete(id);
        if(!company){
            return res.status(404).json({
                status: false,
                message: "Invalid Company Id"
            })
        }

        return res.status(200).json({
            status: true,
            message: "Company is deleted Successfully"
        })
    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message
        })
    }
}


// this controller is responsible to add the companies in the new pool if the company is added in the others part whether it is from organiser panel , app platform
exports.addUnmappedOrNewCompany = async(req, res) => {
    const { company } = req.body;
    try{

        if(!company){
            return res.status(404).json({
                status: false,
                message: 'Company is required field'
            });
        }

        const existing = await UnmappedOrNewCompanyModel.findOne({ company: company.toLowerCase() });

        if(existing){
            return res.status(200).json({
                status: false,
                message: 'Company is already Existed'
            });
        }

        const existingInCompanyMaster = await CompanyMasterModel.findOne({ company: company.toLowerCase() });

        if(existingInCompanyMaster){
            return res.status(200).json({
                status: false,
                message: 'Company is already Existed in company master'
            })
        }
        
        const newEntry = new UnmappedOrNewCompanyModel({ company: company.toLowerCase() });
        newEntry.save();

        return res.status(201).json({ 
            status: true,
            message: 'Added New Entry Successfully'
         });

    }catch(error){
        
        return res.status(500).json({
            status: false,
            message: error.message
        });
    }
}

// list all unmapped or new company
exports.listAddUnmappedOrNewCompany = async(req, res) => {
    try{
        const list = await UnmappedOrNewCompanyModel.find();

        return res.status(200).json({
            status: true,
            data: list,
            message: 'All Unmapped or New company list'
        });

    }catch(error){
        return res.status(500).json({
            status: false,
            message: error.message
        });
    }
}

// delete the entry in unmapped or new company
exports.deleteUnmappedOrNewCompany = async (req, res) => {
    const {id} = req.params;
    try{

        if(!id){
            return res.status(404).json({
                status: false,
                message: 'Please provide the company id'
            });
        }

        await UnmappedOrNewCompanyModel.findByIdAndDelete(id);

        return res.status(200).json({
            status: true,
            message: 'company is deleted successfully'
        });

    }catch(error){
        return res.status(500).json({
            status: false,
            message: error.message
        })
    }
}

// send back the company in pool once it got added in unmapped section
exports.unmappedToUnmappedOrNewCompany = async (req, res) => {

    const unmappedcompany = req.body.company.toLowerCase();

    try{

        const isExisting = await UnmappedOrNewCompanyModel.findOne({ company: unmappedcompany });

        if(isExisting){

            const unmappedCompany = await UnmappedCompanyModel.findOneAndDelete({ company: unmappedcompany, isMapped: false });

            if(!unmappedCompany){
                return res.status(404).json({
                    status: false,
                    message: 'Invalid company'
                });
            }

            return res.status(200).json({
                status: true,
                message: 'Company is already present in unmapped or new company pool'
            });
        }else{
            const newEntry = new UnmappedOrNewCompanyModel({ company: unmappedcompany });
            newEntry.save();

            const unmappedCompany = await UnmappedCompanyModel.findOneAndDelete({ company: unmappedcompany, isMapped: false });

            if(!unmappedCompany){
                return res.status(404).json({
                    status: false,
                    message: 'Invalid company'
                });
            }

            return res.status(201).json({
                status: true,
                message: 'Company is added successfully to unmapped or new company pool'
            });
        }

    }catch(error){
        return res.status(500).json({
            status: false,
            message: error.message
        })
    }
}

// send back the company in a pool once it got added in new company section
exports.companyToUnmappedOrNewCompany = async (req, res) => {
    const company = req.body.company.toLowerCase();
    try{
        
        const exist = await UnmappedOrNewCompanyModel.findOne({ company });
        if(!exist){

            const isPresentInCompanyMaster = await CompanyMasterModel.findOne({ company });

            if(!isPresentInCompanyMaster){
                return res.status(404).json({
                    status: false,
                    message: 'Invalid Company'
                })
            }

            const newEntry = new UnmappedOrNewCompanyModel({ company });
            newEntry.save();

            await CompanyMasterModel.findOneAndDelete({ company });

            return res.status(201).json({
                status: true,
                message: 'Added successfully to the unmapped or new company pool'
            })
        }

        await CompanyMasterModel.findOneAndDelete({ company });

        return res.status(404).json({
            status: false,
            message: 'Already present'
        })


    }catch(error){

        return res.status(500).json({
            status: false,
            message: error.message
        })

    }
}

exports.getAllUserWithLinkedinAndTwitter = async (req, res) => {
    try {
        const allUserWithLinkedinAndTwitterCount = await UserModel.find(
            {
                $or: [
                    { linkedinProfileUrl: { 
                        $ne: null,
                        $nin: [""]
                     } },
                    { xProfileUrl: { 
                        $ne: null,
                        $nin: [""]
                     } }
                ]
            }).countDocuments();
        
        
        const allUserWithLinkedinAndTwitter = await UserModel.find(
            {
                $or: [
                    { linkedinProfileUrl: { 
                        $ne: null,
                        $nin: [""]
                     } },
                    { xProfileUrl: { 
                        $ne: null,
                        $nin: [""]
                     } }
                ]
            },
            {
                linkedinProfileUrl: 1,
                xProfileUrl: 1,
                first_name: 1,
                last_name: 1,
                company: 1,
                designation: 1,
                linkedinFollowers: 1,
                xFollowers: 1
            }
        );

        res.status(200).json({
            success: true,
            count: allUserWithLinkedinAndTwitterCount,
            data: allUserWithLinkedinAndTwitter
        });

    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
}


exports.updateLinkedinCountOrxCount = async (req, res) => {
    const { id } = req.params;
    const { xFollowersCount, linkedinFollowersCount } = req.body;

    try {
        // Build the update object dynamically
        const updateData = {};
        if (xFollowersCount !== undefined) {
            updateData.xFollowers = xFollowersCount;
        }
        if (linkedinFollowersCount !== undefined) {
            updateData.linkedinFollowers = linkedinFollowersCount;
        }

        // Only proceed if there's something to update
        if (Object.keys(updateData).length === 0) {
            return res.status(400).json({
                success: false,
                message: "No valid data provided to update."
            });
        }

        const updateUserLinkedinOrXFollowerCount = await UserModel.updateOne(
            { _id: id },
            { $set: updateData }
        );

        res.status(200).json({
            success: true,
            message: "User follower count(s) updated successfully",
            data: updateUserLinkedinOrXFollowerCount
        });

    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
}