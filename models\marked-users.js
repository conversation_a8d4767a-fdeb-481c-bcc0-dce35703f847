const mongoose = require('mongoose');
const { Schema } = mongoose;

const markedUsersSchema = new mongoose.Schema({
    from_user_id: { 
        type: String,
        required: true,
    }, 
    to_user_id: { 
        type: mongoose.Schema.ObjectId,
        required: true,
    }, 
    status: {
        default: 1,
        type: Number
    } 
}, {
    timestamps: {
        createdAt: 'createdAt', // Use `created_at` to store the created date
        updatedAt: 'updatedAt' // and `updated_at` to store the last updated date
    }
})

module.exports = mongoose.model('marked_users', markedUsersSchema)