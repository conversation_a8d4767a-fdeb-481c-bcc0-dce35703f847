const mongoose = require('mongoose');

const citiesSchema = new mongoose.Schema({
    // city: {
    //     required: true,
    //     type: String
    // },
    id: { type: Number, required: true },
    name: { type: String, required: true },
    state_id: { type: Number, required: true },
    state_code: { type: String, required: true },
    state_name: { type: String, required: true },
    country_id: { type: Number, required: true },
    country_code: { type: String, required: true },
    country_name: { type: String, required: true },
    latitude: { type: String, required: true },
    longitude: { type: String, required: true },
    wikiDataId: { type: String },
    isPrimary: {
        default: 0,
        type: Number
    },
    isPopular: {
        default: 0,
        type: Number
    },
    status: {
        default: 1,
        type: Number
    }
},{
    timestamps: {
      createdAt: 'createdAt', // Use `created_at` to store the created date
      updatedAt: 'updatedAt' // and `updated_at` to store the last updated date
    }
})

module.exports = mongoose.model('cities', citiesSchema)
