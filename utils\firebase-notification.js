const { GoogleAuth } = require('google-auth-library');
const axios = require('axios');
const path = require('path');
const fs = require('fs');

async function getAccessToken() {
  try {
    const keyFilePath = path.join(__dirname, '../services/newServiceKey.json');

    if (!fs.existsSync(keyFilePath)) {
      throw new Error(`Key file does not exist at path: ${keyFilePath}`);
    }

    const serviceAccount = JSON.parse(fs.readFileSync(keyFilePath, 'utf-8'));

    const requiredFields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email', 'client_id'];
    for (const field of requiredFields) {
      if (!serviceAccount[field]) {
        throw new Error(`Missing required field in key file: ${field}`);
      }
    }

    const auth = new GoogleAuth({
      credentials: serviceAccount,
      scopes: ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/firebase.messaging']
    });

    const client = await auth.getClient();
    const accessTokenResponse = await client.getAccessToken();
    if (!accessTokenResponse || !accessTokenResponse.token) {
      throw new Error('Failed to retrieve access token');
    }

    return accessTokenResponse.token;
  } catch (error) {
    console.error('Error getting access token:', error);
    throw error;
  }
}

const FCMMessaging = async (message) => {
  try {
    const accessToken = await getAccessToken();

    const url = `https://fcm.googleapis.com/v1/projects/klout-club/messages:send`; // Replace YOUR_PROJECT_ID
    const headers = {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
    };

    const response = await axios.post(url, message, { headers });
  } catch (error) {
    console.error('Error sending message:', error.response?.data || error.message);
  }
};

const message = {
  message: {
    token: 'FCM_DEVICE_TOKEN', // Replace with actual device token
    notification: {
      title: 'Hello!',
      body: 'This is a test notification.',
    },
    data: {
      key1: 'value1',
      key2: 'value2',
    },
  },
};

module.exports = {
  FCMMessaging,
};

// Example usage
// FCMMessaging(message);