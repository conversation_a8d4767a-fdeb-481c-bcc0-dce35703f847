const express = require("express");
const userroutes = require("./user-routes");
const industryroutes = require("./industry-route");

const countryRoutes = require("./country-route");
const stateRoutes = require('./state-route');

const cityRoutes = require("./city-route");
const skillsRoutes = require("./skill-route");
const uploadRoutes = require("./upload-route");
const notificationRoute = require("./notification-route");
const commonRoute = require("./common-route");
const userChatRoutes = require("./user-chat-routes");
const tlsRoutes = require('./tls-route');
const paymentRoutes = require('./payment-route');
const premiumUserRoutes = require('./premium-user-route');



const router = express.Router();

router.use("/user", userroutes);
router.use("/industry", industryroutes);
router.use("/country", countryRoutes);
router.use("/state", stateRoutes);
router.use("/city", cityRoutes);
router.use("/skill", skillsRoutes);
router.use("/upload", uploadRoutes);
router.use("/notification", notificationRoute);
router.use("/common", commonRoute);
router.use("/user-chat", userChatRoutes);
router.use("/tls", tlsRoutes);
router.use('/payment', paymentRoutes);
router.use('/premium', premiumUserRoutes);


module.exports = router;
