const { sendResponse, distanceBt2Points } = require("../../utils/utility");
const { FCMMessaging } = require("../../utils/firebase-notification");
const UserModel = require("../../models/user-model");
const DeleteUserModel = require("../../models/deleted-user-model");
const ConnectionRequestsModel = require("../../models/connection-requests");
const ConnectionsModel = require("../../models/connections");
const MarkedUser = require("../../models/marked-users");
const OTPTemp = require("../../models/otp-temp");
const Notification = require("../../models/notifications");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const moment = require("moment");
const https = require("https");
const urlencode = require("urlencode");
const nodemailer = require("nodemailer");

const imageBaseUrl = process.env.DOWNLOAD_IMAGE_BASE_URL;

// Function to generate OTP
function generateOTP() {
  return Math.floor(100000 + Math.random() * 900000);
}

const transporter = nodemailer.createTransport({
  service: "gmail",
  host: "smtp.gmail.com",
  port: 465,
  secure: true,
  auth: {
    user: process.env.SMTP_EMAIL,
    pass: process.env.SMTP_PASSWORD,
  },
});

const register = async (req, res) => {
  const {
    first_name,
    last_name,
    email,
    password,
    linkedInId,
    googleId,
    otp,
    step,
  } = req.body;

  if (!email) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "Email is required",
    });
  }

  const oldUser = await UserModel.findOne({
    emailId: email,
  });

  if (oldUser) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "User Already Exist. Please Login",
    });
  }
  try {
    let encryptedPassword = "";

    if (email && password) {
      if (step == "1") {
        const otp = generateOTP();

        const data = new OTPTemp({
          email: req.body.email,
          otp: otp,
        });

        const user = await data.save();

        if (user) {
          const mailOptions = {
            from: {
              name: "Klout Club",
              address: process.env.SMTP_EMAIL,
            },
            to: email,
            subject: "OTP for Registration",
            text: `Your OTP for registration is ${otp}`,
          };

          transporter.sendMail(mailOptions, (error, info) => {
            if (error) {
              return res.status(500).send("Error sending email");
            }

            res.status(200).send("OTP sent successfully");
          });
        }
      } else if (step == "2") {
        if (!otp) {
          return await sendResponse(req, res, {
            successStatus: false,
            statusCode: 200,
            msg: "OTP is required!",
          });
        }

        const verifyOtp = await OTPTemp.findOne({
          email: email,
          otp: otp,
        });

        if (!verifyOtp) {
          return await sendResponse(req, res, {
            successStatus: false,
            statusCode: 400,
            msg: "OTP verification failed.",
          });
        } else {
          encryptedPassword = await bcrypt.hash(password, 10);

          var details = await OTPTemp.deleteOne({
            email: email,
            otp: otp,
          });

          const data = new UserModel({
            first_name: req.body.first_name,
            last_name: req.body.last_name,
            emailId: req.body.email,
            mobileNumber: req.body.mobileNumber,
            profileImage: req.body.profileImage,
            company: req.body.company,
            designation: req.body.designation,
            industryName: req.body.industryName,
            industryId: req.body.industryId,
            // location: req.body.location,
            linkedInId: "",
            googleId: "",
            linkedInAccessToken: req.body.linkedInAccessToken,
            deviceToken: req.body.deviceToken,
            deviceVersion: req.body.deviceVersion,
            deviceType: req.body.deviceType,
            deviceName: req.body.deviceName,
            appVersion: req.body.appVersion,
            latitude: "",
            preferred_skills: "",
            longitude: "",
            city: "",
            cityId: null,
            searchDistanceinKm: 2,
            shareLastSeen: 1,
            whatsAppNotifications: 1,
            status: 1,
            isDeactivate: 0,
            password: encryptedPassword,
          });

          const user = await data.save();

          const token = jwt.sign(
            { user_id: user._id, email },
            process.env.TOKEN_KEY
          );

          return await sendResponse(req, res, {
            successStatus: true,
            statusCode: 200,
            msg: "Otp verified & User registered successfully",
            data: { user },
          });
        }
      }
    } else if (email && googleId) {
      encryptedPassword = await bcrypt.hash(googleId, 10);

      const data = new UserModel({
        first_name: req.body.first_name,
        last_name: req.body.last_name,
        emailId: req.body.email,
        mobileNumber: req.body.mobileNumber,
        profileImage: req.body.profileImage,
        company: req.body.company,
        designation: req.body.designation,
        industryName: req.body.industryName,
        industryId: req.body.industryId,
        // location: req.body.location,
        linkedInId: "",
        googleId: req.body.googleId,
        linkedInAccessToken: req.body.linkedInAccessToken,
        deviceToken: req.body.deviceToken,
        deviceVersion: req.body.deviceVersion,
        deviceType: req.body.deviceType,
        deviceName: req.body.deviceName,
        appVersion: req.body.appVersion,
        latitude: "",
        preferred_skills: "",
        longitude: "",
        city: "",
        cityId: null,
        searchDistanceinKm: 2,
        shareLastSeen: 1,
        whatsAppNotifications: 1,
        status: 1,
        isDeactivate: 0,
        password: encryptedPassword,
      });

      const user = await data.save();

      const token = jwt.sign(
        { user_id: user._id, email },
        process.env.TOKEN_KEY
      );

      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "User registered successfully",
        data: { user, token },
      });
    } else if (email && linkedInId) {
      encryptedPassword = await bcrypt.hash(linkedInId, 10);

      const data = new UserModel({
        first_name: req.body.first_name,
        last_name: req.body.last_name,
        emailId: req.body.email,
        mobileNumber: req.body.mobileNumber,
        profileImage: req.body.profileImage,
        company: req.body.company,
        designation: req.body.designation,
        industryName: req.body.industryName,
        industryId: req.body.industryId,
        // location: req.body.location,
        linkedInId: req.body.linkedInId,
        googleId: "",
        linkedInAccessToken: req.body.linkedInAccessToken,
        deviceToken: req.body.deviceToken,
        deviceVersion: req.body.deviceVersion,
        deviceType: req.body.deviceType,
        deviceName: req.body.deviceName,
        appVersion: req.body.appVersion,
        latitude: "",
        preferred_skills: "",
        longitude: "",
        city: "",
        cityId: null,
        searchDistanceinKm: 2,
        shareLastSeen: 1,
        whatsAppNotifications: 1,
        status: 1,
        isDeactivate: 0,
        password: encryptedPassword,
      });

      const user = await data.save();

      const token = jwt.sign(
        { user_id: user._id, email },
        process.env.TOKEN_KEY
      );

      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "User registered successfully",
        data: { user, token },
      });
    }
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: error.message,
      data: error,
    });
  }
};

const login = async (req, res) => {
  const { email, linkedInId, googleId, password } = req.body;

  if (!email) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "Email is required",
    });
  }

  try {
    if (email && googleId) {
      const user = await UserModel.findOne(
        { emailId: email, googleId: googleId },
        { __v: false }
      );

      if (user) {
        const token = jwt.sign(
          { user_id: user._id, email },
          process.env.TOKEN_KEY
          // {
          //     expiresIn: "1000h",
          // }
        );

        const result = await UserModel.updateOne(
          { emailId: email, googleId: googleId },
          {
            $set: { isDeactivate: 0 },
          }
        );

        return await sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "logged in successfully!",
          data: { user, token },
        });
      }
    } else if (email && linkedInId) {
      const user = await UserModel.findOne(
        { emailId: email, linkedInId: linkedInId },
        { __v: false }
      );

      if (user) {
        const token = jwt.sign(
          { user_id: user._id, email },
          process.env.TOKEN_KEY
          // {
          //     expiresIn: "1000h",
          // }
        );

        const result = await UserModel.updateOne(
          { emailId: email, linkedInId: linkedInId },
          {
            $set: { isDeactivate: 0 },
          }
        );

        return await sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "logged in successfully!",
          data: { user, token },
        });
      }
    } else if (email && password) {
      const user_details = await UserModel.findOne(
        { emailId: email },
        { __v: false }
      );

      if (!user_details) {
        return await sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "logged in successfully!",
          data: { user_details, token },
        });
      }

      bcrypt.compare(password, user_details.password, (err, result) => {
        if (err || !result) {
          return sendResponse(req, res, {
            successStatus: false,
            statusCode: 401,
            msg: "Invalid email or password",
          });
        }

        const token = jwt.sign(
          { user_id: user_details._id, email },
          process.env.TOKEN_KEY
          // {
          //     expiresIn: "1000h",
          // }
        );

        return sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "logged in successfully!",
          data: { user_details, token },
        });
      });
    }
  } catch (err) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: err,
    });
  }
};

const forgetPassword = async (req, res) => {
  const { email, otp, step, password } = req.body;

  try {
    if (!email) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "Email is required",
      });
    }

    const user = await UserModel.findOne({ emailId: email });

    if (!user) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "User not Found",
      });
    }

    // const token = jwt.sign({ email }, process.env.TOKEN_KEY, {
    //   expiresIn: "1000h",
    // });

    if (email) {
      if (step == "1") {
        const otp = generateOTP();

        const data = new OTPTemp({
          email: req.body.email,
          otp: otp,
        });

        const user = await data.save();

        if (user) {
          const mailOptions = {
            from: {
              name: "Klout Club",
              address: process.env.SMTP_EMAIL,
            },
            to: email,
            subject: "Password Reset Password",
            text: `Your OTP for Password Reset is ${otp}`,
          };

          transporter.sendMail(mailOptions, (error, info) => {
            if (error) {
              return res.status(500).send("Error sending email");
            }

            res.status(200).send("OTP Sent successfully");
          });
        }
      } else if (step == "2") {
        if (!otp) {
          return await sendResponse(req, res, {
            successStatus: false,
            statusCode: 200,
            msg: "OTP is required!",
          });
        }

        const verifyOtp = await OTPTemp.findOne({
          email: email,
          otp: otp,
        });

        if (!verifyOtp) {
          return await sendResponse(req, res, {
            successStatus: false,
            statusCode: 400,
            msg: "OTP verification failed.",
          });
        } else {
          encryptedPassword = await bcrypt.hash(password, 10);

          var details = await OTPTemp.deleteOne({
            email: email,
            otp: otp,
          });

          const hashedPassword = await bcrypt.hash(password, 10);

          const r = await UserModel.updateOne(
            { emailId: email },
            { $set: { password: hashedPassword } }
          );

          const token = jwt.sign(
            { user_id: user._id, email },
            process.env.TOKEN_KEY
          );

          return await sendResponse(req, res, {
            successStatus: true,
            statusCode: 200,
            msg: "Otp verified & User password reset successfully",
            data: { user },
          });
        }
      }
    }
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: error.message,
      data: error,
    });
  }
};

const resetPassword = async (req, res) => {
  const { token } = req.params;

  const { newPassword } = req.body;

  jwt.verify(token, process.env.TOKEN_KEY, async (err, decoded) => {
    if (err) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 401,
        msg: "Invalid or Expired token",
      });
    }

    const user = await UserModel.findOne(
      { emailId: decoded.email },
      { __v: false }
    );

    if (!user) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "User not Found",
      });
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10);

    const result = await UserModel.updateOne({
      emailId: email,
      password: hashedPassword,
    });

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Password changed successfully",
    });
  });
};

const changePassword = async (req, res) => {
  const { email, oldPassword, newPassword } = req.body;

  try {
    const user = await UserModel.findOne({ emailId: email });

    if (!user) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 404,
        msg: "User not Found",
      });
    } else {
      bcrypt.compare(oldPassword, user.password, async (err, result) => {
        if (err || !result) {
          return await sendResponse(req, res, {
            successStatus: false,
            statusCode: 401,
            msg: "Invalid old password",
          });
        }

        const hashedPassword = await bcrypt.hash(newPassword, 10);

        const r = await UserModel.updateOne(
          { emailId: email },
          { $set: { password: hashedPassword } }
        );

        return await sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "Password changed successfully",
        });
      });
    }
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: "Internal server error",
    });
  }
};

const resendOtp = async (req, res) => {
  const { email, step } = req.body;

  if (!email) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: "Email is required",
    });
  }

  if (step == "1") {
    var details = await OTPTemp.deleteOne({
      email: email,
    });

    const otp = generateOTP();

    const data = new OTPTemp({
      email: req.body.email,
      otp: otp,
    });

    const user = await data.save();

    if (user) {
      const mailOptions = {
        from: {
          name: "Klout Club",
          address: process.env.SMTP_EMAIL,
        },
        to: email,
        subject: "Resend OTP",
        text: `Your new OTP is is ${otp}`,
      };

      transporter.sendMail(mailOptions, (error, info) => {
        if (error) {
          return res.status(500).send("Error sending email");
        }

        res.status(200).send("OTP sent again successfully");
      });
    }
  }
};

const sendOtp = async (req, res) => {
  const { mobileNumber } = req.body;

  //const result = await UserModel.deleteOne({ mobileNumber: mobileNumber });

  // Validate user input
  if (!mobileNumber) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "Mobile number required!",
    });
  }

  const oldUser = await UserModel.findOne({ mobileNumber: mobileNumber });

  if (oldUser) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "Mobile number already Exist.",
    });
  }

  const min = 100000;
  const max = 999999;

  const otp = (Math.random() * (max - min) + min).toFixed(0);

  const data = new OTPTemp({
    mobileNumber: req.body.mobileNumber,
    otp: otp,
  });

  try {
    const user = await data.save();

    // const accountSid = "**********************************";
    // const authToken = "2a1ed69f8c40e7c1b3ec9ddb31c599db";

    // const client = require("twilio")(accountSid, authToken);

    // client.messages
    //   .create({
    //     body: "Your KLout verification code is:" + otp,
    //     to: "+91" + req.body.mobileNumber, // Text your number
    //     from: "+***********", // From a valid Twilio number
    //   })
    //   .then((message) => console.log(message.sid));

    //Text Local Code
    // var number = "************";

    var apikey = "NWE1MTZmMzY2Nzc5NTc1ODRjNDQ3NjU5NmE1YTczNTY=";

    var sender = "INSKLT";

    const content = `${otp} is your OTP for verifying your profile with KloutClub by Insightner Marketing Services`;

    var msg = encodeURIComponent(content);

    var dataOne =
      "apikey=" +
      apikey +
      "&sender=" +
      sender +
      "&numbers=" +
      mobileNumber +
      "&message=" +
      msg;

    var options = {
      host: "api.textlocal.in",
      path: "/send?" + dataOne,
    };

    callback = function (response) {
      var str = "";

      response.on("data", function (chunk) {
        str += chunk;
      });

      response.on("end", function () {
        console.log(str);
      });
    };

    https.request(options, callback).end();

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Otp sent successfully on your mobile number.",
      data: {},
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 400,
      msg: error.message,
      data: error,
    });
  }
};

const verifyOtp = async (req, res) => {
  const { mobileNumber, OTP } = req.body;

  if (!mobileNumber) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "Mobile number required!",
    });
  }
  if (!OTP) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "OTP is required!",
    });
  }
  const verifyOtp = await OTPTemp.findOne({
    mobileNumber: mobileNumber,
    otp: OTP,
  });
  if (!verifyOtp) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "OTP verification failed.",
    });
  } else {
    var details = await OTPTemp.deleteOne({
      mobileNumber: mobileNumber,
      otp: OTP,
    });
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Otp verify successfully.",
      data: {},
    });
  }
};

const getAllUsers = async (req, res) => {
  try {
    const data = await UserModel.find({}, { password: false, __v: false });

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "User list retrieved successfully",
      data: { data, imageBaseUrl },
    });
  } catch (error) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: error.message,
    });
  }
};

const userUpdate = async (req, res) => {
  try {
    // Get user input
    const request = req.body;
    request.updatedAt = moment().format("YYYY-MM-DD HH:mm:ss");
    const id = req.headers["UserId"] || req.headers["userid"];
    // Validate user input
    if (!id) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 200,
        msg: "User Id is missing.",
      });
    }
    const result = await UserModel.updateOne(
      { _id: id },
      {
        $set: request,
      }
    );
    if (request.awards) {
      var awards = await UserModel.findOne({ _id: id }, { awards: true });
      awards = awards.awards;
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "Data updated successfully!",
        data: { awards },
      });
    }
    if (request.skills) {
      var skills = await UserModel.findOne({ _id: id }, { skills: true });
      skills = skills.skills;
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "Data updated successfully!",
        data: { skills },
      });
    }
    if (request.featured) {
      var featured = await UserModel.findOne({ _id: id }, { featured: true });
      featured = featured.featured;
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "Data updated successfully!",
        data: { featured },
      });
    }
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Data updated successfully!",
      data: {},
    });
  } catch (err) {
    console.log(err);
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: err,
    });
  }
};

const getFullProfileDetails = async (req, res) => {
  try {
    const id = req.headers["UserId"] || req.headers["userid"];
    // Validate user input
    if (!id) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "User Id is missing.",
      });
    }

    var details = await UserModel.findOne(
      { _id: id },
      { password: false, __v: false }
    );
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Data updated successfully!",
      data: { details, imageBaseUrl },
    });
  } catch (err) {
    console.log(err);
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: err,
    });
  }
};

const nearbyProfiles = async (req, res) => {
  try {
    const id = req.headers["UserId"] || req.headers["userid"];

    const { latitude, longitude, distance } = req.body;

    // Validate user input
    if (!id) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "User Id is missing.",
      });
    }

    var userData = await UserModel.find(
      { status: 1, _id: id },
      { password: false, __v: false }
    );

    // console.log(userData);
    //{skills : {$regex:"Multilingual abilities"}, skills : {$regex:"Teamwork and collaboration"}}

    var skillsName = [];
    var industriesName = [];

    if (userData.length) {
      var userSkills = userData[0].skills;

      if (userSkills) {
        userSkills = JSON.parse(userSkills);
        if (userSkills.length) {
          userSkills.map(function (val, key) {
            if (val.SkillName) {
              skillsName.push({ skills: { $regex: val.SkillName } });
            }
          });
        }
      }

      var userIndustries = userData[0].industry;

      if (userIndustries) {
        userIndustries = JSON.parse(userIndustries);
        if (userIndustries.length) {
          userIndustries.map(function (val, key) {
            if (val.IndustryId) {
              skillsName.push({ industryId: val.IndustryId });
            }
          });
        }
      }
    }

    console.log("skillsName", skillsName);
    // var skills = { $or: [{ skills: { $regex: "Multilingual abilities" } }, { skills: { $regex: "Teamwork and collaboration" } }] }

    if (skillsName.length) {
      var list = await UserModel.find(
        {
          status: 1,
          _id: { $ne: id },
          latitude: { $ne: "" },
          longitude: { $ne: "" },
          isDeactivate: 0,
          $or: skillsName,
        },
        { password: false, __v: false }
      );
    } else {
      var list = await UserModel.find(
        {
          status: 1,
          _id: { $ne: id },
          latitude: { $ne: "" },
          longitude: { $ne: "" },
          isDeactivate: 0,
        },
        { password: false, __v: false }
      );
    }

    var nearBy = [];
    var userIds = [];

    if (list) {
      list.map((value, index) => {
        const nearDistance = distanceBt2Points(
          latitude,
          longitude,
          value.latitude,
          value.longitude
        );
        var userData = {
          cityId: value.cityId,
          _id: value._id,
          name: value.name,
          emailId: value.emailId,
          mobileNumber: value.mobileNumber,
          profileImage: value.profileImage,
          company: value.company,
          designation: value.designation,
          industryId: value.industryId,
          location: value.location,
          linkedInId: value.linkedInId,
          linkedInAccessToken: value.linkedInAccessToken,
          deviceToken: value.deviceToken,
          deviceVersion: value.deviceVersion,
          appVersion: value.appVersion,
          deviceType: value.deviceType,
          deviceName: value.deviceName,
          latitude: value.latitude,
          longitude: value.longitude,
          city: value.city,
          status: value.status,
          whatsAppNotifications: value.whatsAppNotifications,
          shareLastSeen: value.shareLastSeen,
          searchDistanceinKm: value.searchDistanceinKm,
          industryName: value.industryName,
          createdAt: value.createdAt,
          updatedAt: value.updatedAt,
          aboutMe: value.aboutMe,
          professionalHighlight: value.professionalHighlight,
          awards: value.awards,
          featured: value.featured,
          skills: value.skills,
          preferred_skills: value.preferred_skills,
        }; //value;
        var inDistance = nearDistance;
        userData.distanceBetween = parseFloat(inDistance).toFixed(2);
        if (nearDistance <= distance) {
          nearBy.push(userData);
          userIds.push(userData._id);
        }
      });
    }

    const filter = { request_user_id: id };

    var connReqList = await ConnectionRequestsModel.find(filter);

    const filter2 = { $or: [{ from_user_id: id }, { to_user_id: id }] };

    var connectionsList = await ConnectionsModel.find(filter2, { __v: false });

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Data list successfully!",
      data: {
        nearByProfile: nearBy,
        connectionRequests: connReqList,
        connectionsList: connectionsList,
      },
    });
  } catch (err) {
    console.log(err);
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: "Error in api",
      error: err,
    });
  }
};

const myConnectionsRequest = async (req, res) => {
  try {
    const id = req.headers["UserId"] || req.headers["userid"];
    // Validate user input
    if (!id) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "User Id is missing.",
      });
    }
    var connReqList = await ConnectionRequestsModel.find(
      { status: 1, request_user_id: id },
      { __v: false }
    );
    var connectionsList = await ConnectionsModel.find(
      { $or: [{ from_user_id: id }, { to_user_id: id }] },
      { __v: false }
    );
    var markedUser = await MarkedUser.find(
      { from_user_id: id },
      { __v: false }
    );

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Data list successfully!",
      data: {
        connectionRequests: connReqList,
        connectionsList: connectionsList,
        markedUser: markedUser,
      },
    });
  } catch (err) {
    console.log(err);
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: err,
    });
  }
};

const getUserProfileDetails = async (req, res) => {
  try {
    const id = req.headers["UserId"] || req.headers["userid"];
    const { userId, latitude, longitude } = req.body;
    // Validate user input
    if (!id) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "User Id is missing.",
      });
    }
    details = await UserModel.findOne(
      { _id: userId },
      { password: false, __v: false }
    );
    if (!details) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "Data not found.",
      });
    }
    const nearDistance = await distanceBt2Points(
      latitude,
      longitude,
      details.latitude,
      details.longitude
    );
    details = {
      cityId: details.cityId,
      _id: details._id,
      name: details.name,
      emailId: details.emailId,
      mobileNumber: details.mobileNumber,
      profileImage: details.profileImage,
      company: details.company,
      designation: details.designation,
      industryId: details.industryId,
      location: details.location,
      linkedInId: details.linkedInId,
      linkedInAccessToken: details.linkedInAccessToken,
      deviceToken: details.deviceToken,
      deviceVersion: details.deviceVersion,
      appVersion: details.appVersion,
      deviceType: details.deviceType,
      deviceName: details.deviceName,
      city: details.city,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
      latitude: details.latitude,
      longitude: details.longitude,
      searchDistanceinKm: details.searchDistanceinKm,
      shareLastSeen: details.shareLastSeen,
      whatsAppNotifications: details.whatsAppNotifications,
      industry: details.industry,
      skills: details.skills,
      preferred_skills: details.preferred_skills,
      aboutMe: details.aboutMe,
      industryName: details.industryName,
      professionalHighlight: details.professionalHighlight,
      awards: details.awards,
      featured: details.featured,
      status: details.status,
      images: details.images,
      distanceBetween: parseFloat(nearDistance).toFixed(2),
    };
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Data updated successfully!",
      data: { details, imageBaseUrl },
    });
  } catch (err) {
    console.log(err);
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: err,
    });
  }
};

const removeMyConnecton = async (req, res) => {
  try {
    const id = req.headers["UserId"] || req.headers["userid"];
    const { userId, connectionId } = req.body;
    // Validate user input
    if (!id) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "User Id is missing.",
      });
    }
    if (!connectionId) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "Request is missing.",
      });
    }
    // $or: [{ from_user_id: id }, { to_user_id: userId }]
    var details = await ConnectionsModel.deleteOne({ _id: connectionId });
    // var details = await ConnectionsModel.deleteOne({ $or: [{ from_user_id: id }, { to_user_id: userId }], $or: [{ from_user_id: userId }, { to_user_id: id }] });
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Connection removed successfully.",
      data: { details },
    });
  } catch (err) {
    console.log(err);
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: err,
    });
  }
};

const markedUser = async (req, res) => {
  try {
    const id = req.headers["UserId"] || req.headers["userid"];
    const { userId, status } = req.body;
    // Validate user input
    if (!id) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "User Id is missing.",
      });
    }
    if (!userId) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "Request is missing.",
      });
    }
    if (status == 1) {
      var details = await MarkedUser.findOne(
        { from_user_id: id, to_user_id: userId },
        { __v: false }
      );
      if (details) {
        return await sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "Connection marked successfully.",
          data: { details },
        });
      }
      try {
        const insertObj = {
          from_user_id: id,
          to_user_id: userId,
          status: 1,
          createdAt: moment().format("YYYY-MM-DD HH:mm:ss"),
          updatedAt: moment().format("YYYY-MM-DD HH:mm:ss"),
        };
        const insertData = new MarkedUser(insertObj);
        const details = await insertData.save();

        return await sendResponse(req, res, {
          successStatus: true,
          statusCode: 200,
          msg: "Connection marked successfully.",
          data: { details },
        });
      } catch (err) {
        console.log(err);
        return await sendResponse(req, res, {
          successStatus: false,
          statusCode: 500,
          msg: err,
        });
      }
    } else {
      var details = await MarkedUser.deleteOne({
        from_user_id: id,
        to_user_id: userId,
      });
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "Connection unmarked successfully.",
        data: { details },
      });
    }
  } catch (err) {
    console.log(err);
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: err,
    });
  }
};

const bookmarkedList = async (req, res) => {
  try {
    const id = req.headers["UserId"] || req.headers["userid"];
    if (!id) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: "User Id is missing.",
      });
    }
    var list = await MarkedUser.aggregate([
      {
        $match: { from_user_id: id },
      },
      {
        $lookup: {
          from: "user",
          localField: "to_user_id",
          foreignField: "_id.str",
          as: "userDetails",
        },
      },
    ]);
    var dataList = [];
    var dataList2 = [];
    if (list) {
      for (const key of Object.keys(list)) {
        // console.log(value)
        try {
          // dataList.push(list[key].to_user_id);
          if (list[key].to_user_id == id) {
            dataList.push(list[key].from_user_id);
          } else {
            dataList.push(list[key].to_user_id);
          }
        } catch (error) {
          console.log(error);
        }
      }
      const userProfiles = await UserModel.find(
        { _id: dataList, isDeactivate: 0 },
        { password: false, __v: false }
      );
      var connReqList = await ConnectionRequestsModel.find(
        { status: 1, request_user_id: id },
        { __v: false }
      );
      var connectionsList = await ConnectionsModel.find(
        { $or: [{ from_user_id: id }, { to_user_id: id }] },
        { __v: false }
      );
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "Bookmarked Connection List.",
        data: {
          markedUsers: list,
          userProfiles,
          connectionRequests: connReqList,
          connectionsList,
        },
      });
    }
  } catch (err) {
    console.log(err);
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: err,
    });
  }
};

const connectionRequest = async (req, res) => {
  const { connectionId } = req.body;
  const id = req.headers["UserId"] || req.headers["userid"];

  if (!connectionId) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "Connection Id required.",
    });
  }
  const userData = await UserModel.findOne({ _id: id });
  const userConnectionIdData = await UserModel.findOne({ _id: connectionId });
  if (!userData) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "User Id is invalid.",
    });
  }
  const resData = await ConnectionRequestsModel.findOne({
    request_user_id: id,
    receive_user_id: connectionId,
  });
  if (!resData) {
    try {
      var insertData = {
        request_user_id: id,
        receive_user_id: connectionId,
        status: 1,
      };
      const data = new ConnectionRequestsModel(insertData);
      const user = await data.save();
      /** Save Notification    */
      var notificationInsertArr = {
        user_id: connectionId,
        from_user_id: id,
        title: userData.name + ", " + userData.designation,
        body: "Has requested you to connect",
        isRead: 0,
        type: 1,
        data: JSON.stringify({
          Type: "1",
          Title: userData.name + ", " + userData.designation,
          Body: "Has requested you to connect",
          ConnectionId: connectionId,
          RequesterId: id,
          RequestId: user._id,
          Name: userData.name,
          Designation: userData.designation,
          ProfileImage: userData.profileImage,
        }),
      };
      const NotificationData = new Notification(notificationInsertArr);
      const NotificationDataArr = await NotificationData.save();
      // console.log(userConnectionIdData);
      if (userConnectionIdData && userConnectionIdData.deviceToken) {
        console.log(userConnectionIdData.deviceToken);
        console.log(userData.deviceToken);
        /** FCM Notification */
        const FCMMessage = {
          //this may vary according to the message type (single recipient, multicast, topic, et cetera)
          to: userConnectionIdData.deviceToken,
          collapse_key: "",

          notification: {
            title: notificationInsertArr.title,
            body: notificationInsertArr.body,
          },
          data: {
            Type: "1",
            Title: userData.name + ", " + userData.designation,
            Body: "Has requested you to connect",
            ConnectionId: connectionId,
            RequesterId: id,
            RequestId: user._id,
            Name: userData.name,
            Designation: userData.designation,
            ProfileImage: userData.profileImage,
          },
        };
        FCMMessaging(FCMMessage);
        /** FCM Notification */
      }
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "Request sent.",
        data: {},
      });
    } catch (error) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 400,
        msg: error.message,
        data: error,
      });
    }
  }
  return await sendResponse(req, res, {
    successStatus: false,
    statusCode: 200,
    msg: "Failed to request sent.",
    data: {},
  });
};

const connectionRequestStatusUpdate = async (req, res) => {
  const { connectionReqId, status, notificationId } = req.body;
  const id = req.headers["UserId"] || req.headers["userid"];
  if (!connectionReqId) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "Connection Id required!",
    });
  }
  const connectionRequestsData = await ConnectionRequestsModel.findOne({
    _id: connectionReqId,
  });
  if (!connectionRequestsData) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "No connection found.",
    });
  } else {
    /**
     * Insert into connection
     */
    if (status == 1) {
      //Accpet
      const data = ConnectionsModel({
        from_user_id: connectionRequestsData.request_user_id,
        to_user_id: connectionRequestsData.receive_user_id,
        is_deleted: 0,
      });
      const result = await data.save(data);
      const userData = await UserModel.findOne({ _id: id });
      const userToData = await UserModel.findOne({
        _id: connectionRequestsData.request_user_id,
      });
      /** Save Notification    */
      var notificationInsertArr = {
        user_id: connectionRequestsData.request_user_id,
        from_user_id: id,
        title: userData.name + ", " + userData.designation,
        body: "Has accepted your request",
        isRead: 0,
        type: 2,
        data: JSON.stringify({
          Type: "2",
          Title: userData.name + ", " + userData.designation,
          Body: "Has accepted your request",
          ConnectionId: connectionRequestsData.receive_user_id,
          RequesterId: id,
          RequestId: connectionReqId,
          Name: userData.name,
          Designation: userData.designation,
          ProfileImage: userData.profileImage,
        }),
      };
      const NotificationData = new Notification(notificationInsertArr);
      const NotificationDataArr = await NotificationData.save();

      if (userToData && userToData.deviceToken) {
        /** FCM Notification */
        const FCMMessage = {
          //this may vary according to the message type (single recipient, multicast, topic, et cetera)
          to: userToData.deviceToken,
          collapse_key: "",

          notification: {
            title: notificationInsertArr.title,
            body: notificationInsertArr.body,
          },
          data: {
            Type: "2",
            Title: userData.name + ", " + userData.designation,
            Body: "Has requested you to connect",
            ConnectionId: connectionRequestsData.receive_user_id,
            RequesterId: id,
            RequestId: connectionReqId,
            Name: userData.name,
            Designation: userData.designation,
            ProfileImage: userData.profileImage,
          },
        };
        FCMMessaging(FCMMessage);
      }
      /** FCM Notification */
    }
    var details = await ConnectionRequestsModel.deleteOne({
      _id: connectionReqId,
    });
    var details = await Notification.deleteOne({ _id: notificationId });

    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Accepted successfully.",
      data: {},
    });
  }
};

const myConnections = async (req, res) => {
  const id = req.headers["UserId"] || req.headers["userid"];
  if (!id) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "Connection Id required!",
    });
  }
  var list = await ConnectionsModel.find(
    { $or: [{ from_user_id: id }, { to_user_id: id }] },
    { __v: false }
  );
  var dataList = [];
  var connectionIdData = [];
  if (list) {
    for (const key of Object.keys(list)) {
      try {
        if (list[key].to_user_id == id) {
          dataList.push(list[key].from_user_id);

          var connectionId = {
            connectionId: list[key]._id,
            userId: list[key].from_user_id,
          };
          connectionIdData.push(connectionId);
        } else {
          dataList.push(list[key].to_user_id);
          var connectionId = {
            connectionId: list[key]._id,
            userId: list[key].to_user_id,
          };
          connectionIdData.push(connectionId);
        }
      } catch (error) {
        console.log(error);
      }
    }
    const userProfiles = await UserModel.find(
      { _id: dataList, isDeactivate: 0 },
      { password: false, __v: false }
    );

    if (!userProfiles) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 200,
        msg: "No connection found.",
      });
    } else {
      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "Connection list",
        data: { connectionIdData, userProfiles },
      });
    }
  }
};

const deactivateAccount = async (req, res) => {
  const id = req.headers["UserId"] || req.headers["userid"];
  const { reason, feedback } = req.body;
  if (!id) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "Connection Id required!",
    });
  }
  var list = await ConnectionsModel.find({ _id: id }, { __v: false });

  var request = {
    isDeactivate: 1,
    reason: reason,
    feedback: feedback,
  };
  if (list) {
    const result = await UserModel.updateOne(
      { _id: id },
      {
        $set: request,
      }
    );
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Account deactivated successfully.",
    });
  }
};

const deleteAccountNow = async (req, res) => {
  const id = req.headers["UserId"] || req.headers["userid"];

  if (id) {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Account deleted successfully.",
    });
  }
};

const deleteAccount = async (req, res) => {
  try {
    const id = req.headers["UserId"] || req.headers["userid"];

    const { reason, feedback } = req.body;

    if (!id) {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 200,
        msg: "Connection Id required!",
      });
    }

    var details = await UserModel.findOne({ _id: id }, { __v: false });

    if (details) {
      // details.id = details.id;
      var details1 = {
        cityId: details.cityId,
        user_id: details._id,
        name: details.name,
        emailId: details.emailId,
        mobileNumber: details.mobileNumber,
        profileImage: details.profileImage,
        company: details.company,
        designation: details.designation,
        industryId: details.industryId,
        location: details.location,
        linkedInId: details.linkedInId,
        linkedInAccessToken: details.linkedInAccessToken,
        deviceToken: details.deviceToken,
        deviceVersion: details.deviceVersion,
        appVersion: details.appVersion,
        deviceType: details.deviceType,
        deviceName: details.deviceName,
        latitude: details.latitude,
        longitude: details.longitude,
        city: details.city,
        status: details.status,
        password: details.password,
        whatsAppNotifications: details.whatsAppNotifications,
        shareLastSeen: details.shareLastSeen,
        searchDistanceinKm: details.searchDistanceinKm,
        industryName: details.industryName,
        createdAt: details.createdAt,
        updatedAt: details.updatedAt,
        aboutMe: details.aboutMe,
        professionalHighlight: details.professionalHighlight,
        awards: details.awards,
        skills: details.skills,
        featured: details.featured,
        images: details.images,
        industry: details.industry,
        preferred_skills: details.preferred_skills,
        reason: reason,
        feedback: feedback,
      };
      const result = await DeleteUserModel(details1).save();

      var details1 = await UserModel.deleteOne({ _id: id });

      return await sendResponse(req, res, {
        successStatus: true,
        statusCode: 200,
        msg: "Account deleted successfully.",
      });
    } else {
      return await sendResponse(req, res, {
        successStatus: false,
        statusCode: 200,
        msg: "No record found.",
      });
    }
  } catch (err) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 500,
      msg: err,
    });
  }
};

const isActiveAccount = async (req, res) => {
  const id = req.headers["UserId"] || req.headers["userid"];
  const { toUserId } = req.body;
  if (!id) {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "Connection Id required!",
    });
  }
  var list = await UserModel.find(
    { _id: toUserId, isDeactivate: 0 },
    { __v: false }
  );
  if (list.length > 0) {
    return await sendResponse(req, res, {
      successStatus: true,
      statusCode: 200,
      msg: "Account is active.",
    });
  } else {
    return await sendResponse(req, res, {
      successStatus: false,
      statusCode: 200,
      msg: "Account is deactivated.",
    });
  }
};

module.exports = {
  isActiveAccount,
  deleteAccount,
  deactivateAccount,
  myConnections,
  connectionRequestStatusUpdate,
  connectionRequest,
  bookmarkedList,
  markedUser,
  removeMyConnecton,
  getUserProfileDetails,
  myConnectionsRequest,
  register,
  login,
  sendOtp,
  verifyOtp,
  getAllUsers,
  getFullProfileDetails,
  nearbyProfiles,
  userUpdate,
  deleteAccountNow,
  forgetPassword,
  resetPassword,
  changePassword,
  resendOtp,
};
