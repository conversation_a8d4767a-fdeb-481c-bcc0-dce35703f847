const express = require('express');
const tlsController = require('../../controllers/v1/tls-controller');

const router = express.Router();

router
    .post('/submit-agenda-rating', tlsController.createAgendaRating)
    .post('/view-tls', tlsController.thoughtLeadershipScore)
    .post('/view-agenda-rating', tlsController.viewAgendaRating)
    .post('/capture-user-tls-count', tlsController.captureUserOnTlsButton)
    .post('/list-tls-users', tlsController.listAllUserClickOnTls)

    //routes for the news publishers
    .get('/get-all-news-publishers', tlsController.getAllNewsPublisher)
    .post('/add-news-publisher', tlsController.addNewsPublisher)
    .patch('/update-news-publisher/:id', tlsController.updateNewsPublisher)
    .get('/view-news-publisher/:id', tlsController.viewNewsPublisher)
    .delete('/delete-news-publisher/:id', tlsController.deleteNewsPublisher);


module.exports = router;