const express = require('express');
const IndustryController = require('../../controllers/v2/industry-controller');
const router = express.Router();
const auth = require("../../middleware/auth");
const {sendResponse} = require("../../utils/utility");

//Post Method
router.post('/create', async (req, res) => {
    const { industryName } = req.body;

    // Validate user input
    if (!industryName) {
        return res.status(400).send({error:"industryName is required!"});
    }
    
    const exist = await IndustryController.findbyIndustryName(industryName);

    if (exist) {
     return await sendResponse( req, res,{statusCode:409, msg : "Industry Name Already Exist, Please try with other name", data: exist})
    }

    try {
        const industry = await IndustryController.insertIndustryData(req);
        return await sendResponse( req, res,{statusCode:200, msg : "Industry add successfully!", data: industry})
    }
    catch (error) {
        return await sendResponse( req, res,{statusCode:400, msg : error.message, data: error})
    }
})

//Get all Method
router.get('/getlist', async (req, res) => {
    try {
        const data = await IndustryController.findAll(req);
        return await sendResponse( req, res,{statusCode:200, msg : "Industry list retrieved successfully", data: data})
    }
    catch (error) {
        return await sendResponse( req, res,{statusCode:500, msg : error.message, data: error})
    }
})

module.exports = router;