const express = require('express');
const router = express.Router();
const eventCheckInController = require('../../../controllers/v1/organiser/eventCheckIn');

router
    .post('/send-otp', eventCheckInController.otp)
    .post('/verify-otp', eventCheckInController.verifyOtp)
    .post('/check-in', eventCheckInController.checkIn)
    .post('/existing-user', eventCheckInController.existingCheckInUser)
    .post('/send-whatsapp', eventCheckInController.sendWhatsapp)
    .post('/last-event-attendee', eventCheckInController.findLastAttendee)
    .post('/update-last-event-attendee', eventCheckInController.updateLastAttendee)
    .post('/get-profile-image-url', eventCheckInController.findEventAttendeeProfileUrl)
    .post('/update-image-segregation-status', eventCheckInController.changeImageSegregationStatus)
    .post('/get-image-segregation-status', eventCheckInController.getImageSegregationStatus)
    .post('/unzip-folder', eventCheckInController.unzipFolder)
    .post('/grouping-photo', eventCheckInController.groupingPhoto)
    .post('/get-event-photo-folders', eventCheckInController.getEventPhotoFolders)
    .post('/get-event-photos', eventCheckInController.getEventPhotos)
    .post('/attendee-checked-in', eventCheckInController.createAttendeeCheckedIn)
    .post('/send-bulk-photo-notification', eventCheckInController.sendBulkPhotoNotification)
    .post('/update-existing-attendee', eventCheckInController.updateAttendeeCheckedIn);
    
module.exports = router