require('dotenv').config();
const nodemailer = require('nodemailer');
const ejs = require('ejs');
const path = require('path');
const AWS = require('aws-sdk');

// Configure AWS SES
AWS.config.update({
  accessKeyId: process.env.AWS_ACCESS_NEW_KEY_ID,     // Replace with your AWS Access Key
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY, // Replace with your AWS Secret Key
  region: process.env.AWS_DEFAULT_REGION                // e.g., 'us-east-1'
});

// Configure Nodemailer to use SES
const transporter = nodemailer.createTransport({
  SES: new AWS.SES({ apiVersion: '2010-12-01' })
});

/**
 * Sends an email with the specified EJS template and variables.
 * @param {string} to - Recipient's email address.
 * @param {string} subject - Email subject.
 * @param {string} templateName - Name of the EJS template.
 * @param {Object} variables - Data to inject into the EJS template.
 */
async function sendTemplateEmail(to, subject, templateName, variables) {
  const templatePath = path.join(__dirname, '../emailTemplates', `${templateName}.ejs`);

  try {
    // Render the EJS template with provided variables
    const htmlContent = await ejs.renderFile(templatePath, variables);

    const mailOptions = {
      from: `Klout Club<${process.env.MAIL_FROM_ADDRESS}>`,
      to,
      subject,
      html: htmlContent,
      // To explicitly use SES for sending
      ses: {
        // optional extra SES parameters here
        Tags: [
          {
            Name: 'environment',
            Value: 'production'
          }
        ]
      }
    };

    // Send the email using AWS SES
    return transporter.sendMail(mailOptions);
  } catch (error) {
    console.error('Error rendering email template:', error);
    throw error;
  }
}

module.exports = { sendTemplateEmail };
