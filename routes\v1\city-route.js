const express = require('express');
const router = express.Router();
const CityController = require('../../controllers/v1/city-controller');
// const auth = require("../../middleware/auth");
// const {sendResponse} = require("../../utils/utility");

//Post Method
router.post('/add', async (req, res) => {
    const exist = await CityController.create(req, res);
})

// Get all cities by State ID and Counntry ID
router.post('/getCitiesByStateId', async (req, res) => {
    const exist = await CityController.getCitiesByStateId(req, res);
});

// Get all cities by State ID and Country ID
router.post('/getCitiesByStateCountryId', async (req, res) => {
    const exist = await CityController.getCitiesByStateCountryId(req, res);
});

//Get all Method
router.get('/getAll', async (req, res) => {
    const exist = await CityController.getAll(req, res);
})
//find one  Method
router.get('/findone', async (req, res) => { 
    const exist = await CityController.details(req, res);
})
// Search 
router.get('/search', async (req, res) => { 
    const exist = await CityController.search(req, res);
})

module.exports = router;