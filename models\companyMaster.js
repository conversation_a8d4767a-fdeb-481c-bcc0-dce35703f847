const mongoose = require('mongoose');


const newsSchema = new mongoose.Schema({
    title: String,
    link: String,
});

const companyMasterSchema = new mongoose.Schema({
    profileUrl: {
        type: String,
        required: [true, 'profile name is required field']
    },
    company: {
        type: String,
        required: [true, 'company name is required field']
    },
    website: String,
    industry: String,
    companySize: String,
    headquarters: String,
    specialities: String,
    overview: String,
    mappedTo: [String],
    companyLogo: String,
    news: [newsSchema]

},{timestamps: true});


module.exports = mongoose.model("company_master", companyMasterSchema);