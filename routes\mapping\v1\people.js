const express = require('express');
const router = express.Router();
const peopleController = require('../../../controllers/v1/mapping/people');


router.post('/add-people', peopleController.addPeople)
    .get('/all-people', peopleController.getAllPeople)
    .post('/search-people', peopleController.getSearchPeople)
    .post('/get-contact', peopleController.getContactInfoUsingRocketReach)
    .post('/update-phone-email', peopleController.updateContactInfo)
    .post('/get-contact-signal', peopleController.getContactInfoUsingSignalHire);


module.exports = router