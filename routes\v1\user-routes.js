const express = require("express");
const UserModel = require("../../models/user-model");
const router = express.Router();
const auth = require("../../middleware/auth");
const { sendResponse } = require("../../utils/utility");
const UserController = require("../../controllers/v1/user-controller");

router.get("/users", async (req, res) => {
  const exist = await UserController.userList(req, res);
});

router.get("/users/:id", async (req, res) => {
  const exist = await UserController.userListById(req, res);
});

router.delete("/users/:id", async (req, res) => {
  const exist = await UserController.userDelete(req, res);
});

router.post("/register", async (req, res) => {
  const exist = await UserController.register(req, res);
});

router.post("/login", async (req, res) => {
  const exist = await UserController.login(req, res);
});

router.post("/forgetPassword", async (req, res) => {
  const exist = await UserController.forgetPassword(req, res);
});

router.post("/reset-password/:token", async (req, res) => {
  const exist = await UserController.resetPassword(req, res);
});

router.post("/changepassword", async (req, res) => {
  const exist = await UserController.changePassword(req, res);
});

router.post("/resendOtp", async (req, res) => {
  const exist = await UserController.resendOtp(req, res);
});

router.get("/getAllUsers", async (req, res) => {
  const exist = await UserController.getAllUsers(req, res);
});

router.post("/get-all-users", auth, async (req, res) => {
  const exist = await UserController.getAllUsers(req, res);
});

router.post("/userUpdate", auth, async (req, res) => {
  const exist = await UserController.userUpdate(req, res);
});

router.post("/checkUserAccount", auth, async (req, res) => {
  const exist = await UserController.checkUserAccount(req, res);
});

router.post("/getFullProfileDetails", auth, async (req, res) => {
  const exist = await UserController.getFullProfileDetails(req, res);
});

router.post("/nearbyProfiles", auth, async (req, res) => {
  const exist = await UserController.nearbyProfiles(req, res);
});

router.post("/myConnectionsRequest", auth, async (req, res) => {
  const exist = await UserController.myConnectionsRequest(req, res);
});

router.post("/getUserProfileDetails", auth, async (req, res) => {
  const exist = await UserController.getUserProfileDetails(req, res);
});

router.post("/removeMyConnecton", auth, async (req, res) => {
  const exist = await UserController.removeMyConnecton(req, res);
});

router.post("/markedUser", auth, async (req, res) => {
  const exist = await UserController.markedUser(req, res);
});

router.post("/bookmarkedList", auth, async (req, res) => {
  const exist = await UserController.bookmarkedList(req, res);
});

router.post("/sendOtp", async (req, res) => {
  const exist = await UserController.sendOtp(req, res);
});

router.post("/verifyOtp", async (req, res) => {
  const exist = await UserController.verifyOtp(req, res);
});

router.post("/connectionRequest", auth, async (req, res) => {
  const exist = await UserController.connectionRequest(req, res);
});

router.post("/cancelConnectionRequest", auth, async (req, res) => {
  const exist = await UserController.cancelConnectionRequest(req, res);
});

router.post("/connectionRequestStatusUpdate", auth, async (req, res) => {
  const exist = await UserController.connectionRequestStatusUpdate(req, res);
});

router.post("/myConnections", auth, async (req, res) => {
  const exist = await UserController.myConnections(req, res);
});

router.post("/deactivate", auth, async (req, res) => {
  const exist = await UserController.deactivateAccount(req, res);
});

router.post("/deleteAccount", auth, async (req, res) => {
  const exist = await UserController.deleteAccount(req, res);
});

router.post("/deleteAccountNow", auth, async (req, res) => {
  const exist = await UserController.deleteAccountNow(req, res);
});

router.post("/isActiveAccount", auth, async (req, res) => {
  const exist = await UserController.isActiveAccount(req, res);
});

router.get("/checklogin", auth, async (req, res) => {
  return await sendResponse(req, res, {
    statusCode: 200,
    msg: "Welcome 🙌 to Klout APIs",
  });
});

router.post("/blockUser", auth, async (req, res) => {
  const exist = await UserController.blockUser(req, res);
});

router.get("/blockAppUser", auth, async (req, res) => {
  return await UserController.blockAppUser(req, res); 
}); 

router.get('/adminViewUserDetails/:id', async (req, res) => {
  const exist =  await UserController.adminViewUserDetails(req, res);
});

router.get('/adminDeleteUser/:id', async (req, res) => {
  const exist =  await UserController.adminDeleteUser(req, res);
});


router.get('/adminBlockUser/:id', async (req, res) => {
  const exist =  await UserController.adminBlockUser(req, res);
});

router.post('/blockListUsers', auth, async (req, res) => {
  const exist =  await UserController.blockListUsers(req, res);
});

router.post('/unblockUser', auth, async (req, res) => {
  const exist =  await UserController.unblockUser(req, res);
});

router.post('/valid-user', async (req, res) => {
  const exist =  await UserController.isValidUser(req, res);
});



module.exports = router;
