const express = require('express');
const UserModel = require('../../models/user-model');
const router = express.Router();
const auth = require("../../middleware/auth");
const {sendResponse} = require("../../utils/utility");
const NotificationController = require('../../controllers/v1/notification-controller');

//Post Method
router.post('/list', async (req, res) => {
  const exist = await NotificationController.allList(req, res);
});

router.post('/markStatusRead', async (req, res) => {
  const exist = await NotificationController.markStatusRead(req, res);
});

router.post('/unreadCount', async (req, res) => {
  const exist = await NotificationController.unreadCount(req, res);
});

router.post('/viewProfileNotify', async (req, res) => {
  const exist = await NotificationController.viewProfileNotify(req, res);
});

router.post('/filterList', async (req, res) => {
  const exist = await NotificationController.filterList(req, res);
})
 
module.exports = router;