const express = require('express');
const Model = require('../../models/skill-model');
const router = express.Router();
const auth = require("../../middleware/auth");
const {sendResponse} = require("../../utils/utility");

//Post Method
router.post('/create', async (req, res) => {
    const { skill, skillDesc, status } = req.body;

    // Validate user input
    if (!skill) {
        return res.status(400).send({error:"skill is required!"});
    }

    const exist = await Model.findOne({ skill });

    if (exist) {
     return await sendResponse( req, res,{statusCode:409, msg : "Skill Already Exist, Please try with other name", data: exist})
    }

    const data = new Model({
        skill: skill,
        skillDesc: skillDesc,
        status: status
    })

    try {
        const result = await data.save();
        return await sendResponse( req, res,{statusCode:200, msg : "Skill added successfully!", data: result})
    }
    catch (error) {
        return await sendResponse( req, res,{statusCode:400, msg : error.message, data: error})
    }
})

//Get all Method
router.get('/getlist', async (req, res) => {
    try {
        const data = await Model.find();
        return await sendResponse( req, res,{statusCode:200, msg : "Skills list retrieved successfully", data: data})
    }
    catch (error) {
        return await sendResponse( req, res,{statusCode:500, msg : error.message, data: error})
    }
})

//Get one reacords Method
router.get('/findone', async (req, res) => {
    try {
        const { skill } = req.body;

        // Validate user input
        if (!(skill)) {
            return await sendResponse( req, res,{statusCode:400, msg : "skill is required!"})
        }
        
        const data = await Model.findOne({ skill });
        return await sendResponse( req, res,{statusCode:200, msg : "Skill  retrieved successfully", data: data})
    }
    catch (error) {
        return await sendResponse( req, res,{statusCode:500, msg : error.message, data: error})
    }
})

module.exports = router;