const axios = require('axios');

const sendWhatsappMessage = async (mobileNumber, templateName, bodyValues) => {
    try{
        const body = {
            "countryCode": "+91",
            "phoneNumber": `${mobileNumber}`,
            "callbackData": "some text here",
            "type": "Template",
            "template": {
                "name": templateName,
                "languageCode": "en",
                "bodyValues": [...bodyValues]
            }
        }
        const response = await axios.post('https://api.interakt.ai/v1/public/message/', body, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Basic ${process.env.INTERAKT_API_KEY}`
            }
        });

        return response;

    }catch(error){
        console.log(error);
    }
}

module.exports = sendWhatsappMessage;