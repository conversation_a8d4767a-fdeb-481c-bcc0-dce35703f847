const mongoose = require('mongoose');

const sendEmailMessageSchema = new mongoose.Schema({
    eventUUID: {
        type: String,
        required: true
    },
    firstName: {
        type: String,
        required: true
    },
    userID: {
        type: String,
        required: true
    },
    templateName: {
        type: String,
        required: true
    },
    messageID: {
        type: String,
        ref: 'email_message_status',
        required: true
    },
});

module.exports = mongoose.model('send_email_message', sendEmailMessageSchema)


