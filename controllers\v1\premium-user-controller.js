const UserModel = require('../../models/user-model');


exports.markPremiumUnderBasicPlan = async (req, res) => {
    const { mobileNumber } = req.body;
    try {
        if (!mobileNumber) {
            return res.status(404).json({
                status: false,
                message: 'Provide Valid Number'
            });
        }
        const updateUser = await UserModel.findOneAndUpdate(
            { mobileNumber }, 
            { role: "premium", planExpiryDays: 180 , lastDecrementDate: new Date()},
            
        );

        if (!updateUser) {
            return res.status(404).json({
                status: false,
                message: 'Something went wrong'
            });
        }

        return res.status(200).json({
            status: true,
            message: 'User marked premium successfully'
        });

    } catch (error) {
        return res.status(500).json({
            status: false,
            message: error.message
        });
    }
};

exports.reducePlanExpiryDays = async () => {
    try {
        const today = new Date().toISOString().slice(0, 10);
        const users = await UserModel.find({ planExpiryDays: { $gt: 0 } });

        console.log(users);

        for (let user of users) {
            const lastDecrement = user.lastDecrementDate?.toISOString().slice(0, 10);
            
            if (lastDecrement !== today ){
                let newDays = user.planExpiryDays - 1;
                let newRole = user.role;

                if (newDays === 0) {
                    newRole = "user";
                }

                await UserModel.updateOne(
                    { _id: user._id },
                    { planExpiryDays: newDays, role: newRole, lastDecrementDate: new Date() },
                    
                );
            }
            
        }

        console.log("Cron job executed successfully.");                     
    } catch (error) {
        console.error("Error in cron job:", error.message);
    }
};

// Schedule the job to run daily at midnight
// schedule.scheduleJob("0 0 * * *", exports.reducePlanExpiryDays);

