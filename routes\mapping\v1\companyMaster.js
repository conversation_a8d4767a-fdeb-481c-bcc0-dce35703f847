const express = require('express');
const router = express.Router();
const companyMasterController = require('../../../controllers/v1/mapping/companyMaster');


router
    .post('/add-company' , companyMasterController.addCompany)
    .patch('/update-company/:id', companyMasterController.updateCompany)
    .get('/view-company/:id', companyMasterController.viewCompany)
    .delete('/delete-company/:id', companyMasterController.deleteCompany)
    .get('/all-company', companyMasterController.getAllCompany)
    .post('/add-unmapped-country', companyMasterController.addUnmappedCompany) // this should be delete
    .post('/add-unmapped-company', companyMasterController.addUnmappedCompany)
    .post('/mapping-unmapped-company', companyMasterController.mappingUnmappedCompany)
    .get('/get-unmapped-company', companyMasterController.getUnmappedCompany)
    .delete('/delete-unmapped-company/:id', companyMasterController.deleteUnmappedCompany)


    .post('/add-unmapped-or-new-company', companyMasterController.addUnmappedOrNewCompany)
    .post('/get-all-unmapped-or-new-company', companyMasterController.listAddUnmappedOrNewCompany)
    .delete('/delete-unmapped-or-new-company/:id', companyMasterController.deleteUnmappedOrNewCompany)
    .post('/send-back-unmapped-to-unmapped-or-new-company', companyMasterController.unmappedToUnmappedOrNewCompany)
    .post('/send-back-new-company-to-unmpapped-or-new-company', companyMasterController.companyToUnmappedOrNewCompany)
    .post('/unmapped-mapped-company', companyMasterController.unmappedMappedCompany)
    .post('/view-all-user-with-linkedin-or-twitter', companyMasterController.getAllUserWithLinkedinAndTwitter)
    .patch('/update-user-linkedin-or-x-follower-count/:id', companyMasterController.updateLinkedinCountOrxCount);

module.exports = router