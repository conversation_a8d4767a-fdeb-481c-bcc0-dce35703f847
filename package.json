{"name": "klout-api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "nodemon index.js"}, "author": "", "license": "ISC", "dependencies": {"adm-zip": "^0.5.16", "archiver": "^7.0.1", "aws-sdk": "^2.1691.0", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "csv": "^6.3.9", "csv-parse": "^5.5.6", "csv-parser": "^3.0.0", "csvtojson": "^2.0.10", "dotenv": "^16.0.0", "ejs": "^3.1.10", "express": "^4.17.3", "express-fileupload": "^1.5.0", "fast-csv": "^5.0.1", "fcm-node": "^1.3.0", "firebase-admin": "^12.1.0", "fs": "^0.0.1-security", "google-auth-library": "^9.14.1", "handlebars": "^4.7.8", "https": "^1.0.0", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^6.2.2", "multer": "^1.4.5-lts.1", "node-schedule": "^2.1.1", "nodemailer": "^6.9.13", "nodemon": "^2.0.15", "openai": "^4.95.1", "payu-websdk": "^1.2.0", "pg": "^8.11.5", "pm2": "^5.4.3", "request": "^2.88.2", "request-promise": "^0.0.1", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.0.0", "swagger-ui-express": "^5.0.0", "twilio": "^4.14.0", "urlencode": "^2.0.0", "xlsx": "^0.18.5", "xml2js": "^0.6.2"}}