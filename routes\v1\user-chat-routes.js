const express = require('express');
const UserModel = require('../../models/user-model');
const UserChatModel = require('../../models/user-chat-model');
const router = express.Router();
const auth = require("../../middleware/auth");
const {sendResponse} = require("../../utils/utility");
const UserController = require('../../controllers/v1/user-controller');
const UserChatController = require('../../controllers/v1/user-chat-controller');

router.get('/chatUsersList', async (req, res) => {
  const exist = await UserChatController.chatUsersList(req, res);
});

router.post('/sendMessage', async (req, res) => {
  const exist = await UserChatController.sendMessage(req, res);
});

router.post('/updateChatUserStatus', async (req, res) => {
  const exist = await UserChatController.updateChatUserStatus(req, res);
});

module.exports = router;