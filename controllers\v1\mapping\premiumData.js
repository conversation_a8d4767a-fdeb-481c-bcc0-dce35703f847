const xlsx = require('xlsx');
const csvParser = require('csv-parser');
const { createReadStream, unlink } = require('fs');
const PremiumDataModel = require('../../../models/permiumData');

exports.uploadPremiumData = async (req, res) => {
    try {
        if (!req.files || !req.files.file) {
            return res.status(400).send('Please upload a file');
        }

        const file = req.files.file;
        const path = `./uploads/${Date.now()}-${file.name}`;

        // Move the file to the server
        file.mv(path, async (err) => {
            if (err) {
                return res.status(500).send('Error saving file');
            }

            let data = [];

            // Check file extension to determine parsing method
            if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                // Handle Excel files
                const workbook = xlsx.readFile(path);
                const sheetNames = workbook.SheetNames;
                const sheet = workbook.Sheets[sheetNames[0]];
                data = xlsx.utils.sheet_to_json(sheet);
                await processDataAndSave(data, res, path);
            } else if (file.name.endsWith('.csv')) {
                // Handle CSV files
                createReadStream(path)
                    .pipe(csvParser())
                    .on('error', (error) => {
                        unlink(path, () => { }); // Clean up the file
                        return res.status(500).send('Error parsing CSV file');
                    })
                    .on('data', (row) => {
                        data.push(row);
                    })
                    .on('end', async () => {
                        await processDataAndSave(data, res, path);
                    });
                return; // Exit early to avoid processing CSV data twice
            } else {
                // Unsupported file format
                unlink(path, () => { }); // Clean up the file
                return res.status(400).send('Unsupported file format');
            }
        });
    } catch (error) {
        return res.status(400).json({
            status: false,
            message: error.message,
        });
    }
};

const processDataAndSave = async (data, res, path) => {
    try {
        for (const row of data) {
            const document = {
                first_name: row.first_name || "",
                last_name: row.last_name || "",
                job_title: row.job_title || "",
                company_name: row.company_name || "",
                industry: row.industry || "",
                email: row.email || "",
                phone_number: row.phone_number || "",
                alternate_mobile_number: row.alternate_mobile_number || "",
                website: row.website || "",
                employee_size: row.employee_size || "",
                company_turn_over: row.company_turn_over || "",
                linkedin_page_link: row.linkedin_page_link || "",
                country: row.country || "",
                job_function: row.job_function || ""
            };

            // Update or create the document
            await PremiumDataModel.findOneAndUpdate(
                {
                    email: document.email,
                    phone_number: document.phone_number
                },
                document,
                {
                    upsert: true,  // Create the document if it doesn't exist
                    new: true,     // Return the updated document
                    runValidators: true // Ensure the update runs validators
                }
            );
        }

        // Clean up uploaded file
        unlink(path, (err) => {
            if (err) console.error('Error deleting file:', err);
        });

        return res.status(200).json({
            status: true,
            message: 'Data uploaded successfully',
        });
    } catch (error) {
        console.error('Error while saving data:', error);
        return res.status(500).json({
            status: false,
            message: 'Error while saving data',
        });
    }
};

exports.all = async (req, res) => {
    try{
        const allData = await PremiumDataModel.find();
        
        return res.status(200).json({
            status: true,
            data: allData,
            message: 'All Premium Data'
        })
    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message
        })
    }
}

exports.update = async (req, res) => {
    const { id } = req.body;
    try{
        const updateData = await PremiumDataModel.findOneAndUpdate({ _id: id }, req.body, { new: true });

        if(!updateData){
            return res.status(400).json({
                status: false,
                message: 'Bad Request'
            })
        }

        return res.status(200).json({
            status: true,
            data: updateData,
            message: 'Record updated successfully'
        })
    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message
        })
    }
}

// 10-03-2025  (update code to store the attendee from php to mapping)

exports.AttendeeUpload = async (req, res) => {
    try {
        const {
            first_name,
            last_name,
            job_title,
            company_name,
            industry,
            email,
            phone_number,
            alternate_mobile_number,
            website,
            employee_size,
            company_turn_over,
            linkedin_page_link,
            country,
            job_function
        } = req.body;

        // Find existing record by email or phone_number
        const existingRecord = await PremiumDataModel.findOne({
            $or: [{ email }, { phone_number }]
        });

        if (existingRecord) {
            // Update only the fields that are provided
            existingRecord.first_name = first_name || existingRecord.first_name;
            existingRecord.last_name = last_name || existingRecord.last_name;
            existingRecord.job_title = job_title || existingRecord.job_title;
            existingRecord.company_name = company_name || existingRecord.company_name;
            existingRecord.industry = industry || existingRecord.industry;
            existingRecord.alternate_mobile_number = alternate_mobile_number || existingRecord.alternate_mobile_number;
            existingRecord.website = website || existingRecord.website;
            existingRecord.employee_size = employee_size || existingRecord.employee_size;
            existingRecord.company_turn_over = company_turn_over || existingRecord.company_turn_over;
            existingRecord.linkedin_page_link = linkedin_page_link || existingRecord.linkedin_page_link;
            existingRecord.country = country || existingRecord.country;
            existingRecord.job_function = job_function || existingRecord.job_function;
            existingRecord.updatedAt = new Date();

            await existingRecord.save();
            return res.status(200).json(
                { success: true,
                  message: "Attendee updated successfully." 
                });
        } else {
            // Create new attendee if no existing record is found
            const newAttendee = new PremiumDataModel({
                first_name: first_name || "",
                last_name: last_name || "",
                job_title: job_title || "",
                company_name: company_name || "",
                industry: industry || "",
                email: email || "",
                phone_number: phone_number || "",
                alternate_mobile_number: alternate_mobile_number || "",
                website: website || "",
                employee_size: employee_size || "",
                company_turn_over: company_turn_over || "",
                linkedin_page_link: linkedin_page_link || "",
                country: country || "",
                job_function: job_function || "",
                createdAt: new Date(),
                updatedAt: new Date()
            });

            await newAttendee.save();
            return res.status(201).json({ 
                success: true,
                message: "Attendee saved successfully." 
            });
        }
    } catch (error) {
        console.error("Error processing attendee:", error);
        return res.status(500).json({ 
            success: false, 
            message: "Internal Server Error" 
        });
    }
};


