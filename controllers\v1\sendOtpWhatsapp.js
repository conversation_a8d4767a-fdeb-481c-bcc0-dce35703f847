const axios = require('axios');

async function sendOtpWhatsapp(to, otp) {
    try {
        // Define your Interakt API endpoint
        const interaktEndpoint = 'https://api.interakt.ai/v1/public/message/';

        // Define the headers for the API request
        const headers = {
            'Authorization': `Basic ${process.env.INTERAKT_API_KEY}`, // Add your Interakt API key in environment variables
            'Content-Type': 'application/json',
        };
        const requestBody = {
            countryCode: '+91',
            phoneNumber: to,
            type: 'Template',
            template: {
                    name: 'otp_verification',
                    languageCode: 'en',
                    bodyValues: [`${otp}`],
                    buttonValues: {
                        "0": [`${otp}`]
                    }
            }
        };

        // Make the API request
        const response = await axios.post(interaktEndpoint, requestBody, { headers });

        // Handle the response
        if (response.data && response.data.success) {
            console.log('OTP sent successfully:', response.data);
            return { success: true, message: 'Whatsapp OTP sent successfully', data: response.data };
        } else {
            console.error('Failed to send OTP:', response.data);
            return { success: false, message: 'Failed to send whatsapp OTP', data: response.data };
        }
    } catch (error) {
        console.error('Error in sending OTP:', error);
        return { success: false, message: 'Error in sending OTP', error: error.message };
    }
}

module.exports =  sendOtpWhatsapp;
