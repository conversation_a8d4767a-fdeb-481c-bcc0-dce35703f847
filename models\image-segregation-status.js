const mongoose = require('mongoose');

const imageSegregationStatusSchema = new mongoose.Schema({
    eventUUID: {
        required: true,
        type: String,
    },
    status: {
        type: String,
    },
    finalStatus: {
        type: Boolean
    },
    message: {
        type: String
    }
},{
    timestamps: true
})

module.exports = mongoose.model('imageSegregationStatus', imageSegregationStatusSchema)