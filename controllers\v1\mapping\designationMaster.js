const DesignationMasterModel = require('../../../models/designationMaster');
const UnmappedDesignationModel = require('../../../models/unmappedDesignation')
const UserModel = require('../../../models/user-model');
const UnmappedOrNewDesignationModel = require('../../../models/unmappedOrNewDesignation');


// created add designation to the designation master 
exports.addDesignation = async(req, res) => {
    const { designation, mappedTo } = req.body;
    try{
        const existingDesignation = await DesignationMasterModel.findOne({ designation: designation.toLowerCase() });

        if(existingDesignation){
            return res.status(404).json({
                status: false,
                message: 'Designation is already Added'
            })
        }
        const data = new DesignationMasterModel( { designation: designation.toLowerCase(), mappedTo } );
        const isSaveDesignation = await data.save();

        if(!isSaveDesignation){
            return res.status(404).json({
                status: false,
                message: 'Something Wrong'
            })
        }

        await UnmappedOrNewDesignationModel.findOneAndDelete({ designation: designation.toLowerCase() });

        return res.status(201).json({
            status: true,
            message: 'Designation is added to the list'
        })

    }catch(error){
        return res.status(400).json({
            status: false,
            message: error.message
        })
    }
}

exports.updateDesignation = async (req, res) => {
    const { id } = req.params;
    const updatedData = req.body;

    try {
        // Convert designation name to lowercase if provided
        if (updatedData.designation) {
            updatedData.designation = updatedData.designation.toLowerCase();
        }

        // Find the existing designation data
        const previousData = await DesignationMasterModel.findById(id);
        if (!previousData) {
            return res.status(404).json({
                status: false,
                message: "Designation not found",
            });
        }

        const previousDesignation = previousData.designation;

        // Update designation data
        const update = await DesignationMasterModel.findByIdAndUpdate(id, updatedData, {
            new: true,
        });

        if (!update) {
            return res.status(404).json({
                status: false,
                message: "Designation not found",
            });
        }

        res.status(200).json({
            status: true,
            data: update,
            message: "Data is updated successfully",
        });

        // If designation name was updated, update users with the previous designation name
        if (updatedData.designation && updatedData.designation !== previousDesignation) {
            await UserModel.updateMany(
                { designation: previousDesignation },
                { $set: { designation: updatedData.designation } }
            );
        }

    } catch (error) {
        return res.status(500).json({
            status: false,
            message: error.message,
        });
    }
};


// show the designation in designation masters
exports.viewDesignation = async(req, res) => {
    const { id } = req.params;

    try{
        const designation = await DesignationMasterModel.findById(id);

        if(!designation){
            return res.status(400).json({
                status: false,
                message: 'Designation not found',
            })
        }

        return res.status(200).json({
            status: true,
            data: designation,
            message: "View Designation"
        });
    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message,
        })
    }
}

//delete the designation
exports.deleteDesignation = async (req, res) => {
    const { id } = req.params;
    
    try{
        const deleteDesignation = await DesignationMasterModel.findByIdAndDelete(id);

        if(!deleteDesignation){
            return res.status(404).json({
                status: false,
                message: "Designation not found"
            });
        }

        return res.status(200).json({
            status: true,
            message: "Designation is deleted successfully"
        });


    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message
        })
    }
}

exports.getAllDesignation = async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const search = req.query.search || "";
    const limit = 50;
    const skip = (page - 1) * limit;

    try{
        const query = search ? { designation: { $regex: search, $options: 'i' } } : {};

        const designations = await DesignationMasterModel
                                                .find(query)
                                                .limit(limit)
                                                .skip(skip)
                                                .select('designation mappedTo');
        
        const totalDesignations = await DesignationMasterModel.countDocuments();

        return res.status(200).json({
            status: true,
            data: { designations, totalDesignations },
            message: 'All Designations'
        })


    }catch(error){

        return res.status(404).json({
            status: false,
            message: error.message
        })

    }
}

exports.getUserDesignations = async (req, res) => {
    try {
      // Fetch all users and project only the 'designtion' field
      const users = await UserModel.find({}, { designation: 1, _id: 0 });
  
      // Extract the designation field into an array
      const designations = users.map(user => user.designation);
  
      // Respond with the array of designations
      res.status(200).json({ success: true, designations });
    } catch (error) {
      console.error("Error fetching designations:", error);
      res.status(500).json({ success: false, message: "Internal server error" });
    }
};
  

exports.addUnmappedDesignation = async (req, res) => {
    const { designation, isMapped, mappedWith } = req.body;
    try{
        const isExisting = await UnmappedDesignationModel.findOne({ designation });
        if(isExisting){
            const isExistingMapped = await UnmappedDesignationModel.findOne({ designation, isMapped: true});
            if(isExistingMapped){

                await UserModel.updateMany(
                    { designation: designation },
                    { $set: { designation: isExisting.mappedWith } }
                );

                return res.status(200).json({
                    status: true,
                    message: 'Designation is already Existed'
                })
            }

            return res.status(200).json({
                status: true,
                message: 'Designation is already Existed'
            })
        }

        const newDesignation = new UnmappedDesignationModel({ designation, isMapped, mappedWith });
        await newDesignation.save();

        await UnmappedOrNewDesignationModel.findOneAndDelete({ designation });

        return res.status(201).json({
            status: true,
            message: 'Designation is added successfully'
        })
    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message
        })
    }
}

exports.mappingUnmappedDesignation = async(req, res) => {
    const { unmappedDesignation, mappedDesignation } = req.body

    try{
        const searchMappedDesignation = await DesignationMasterModel.findOne({ designation: mappedDesignation });
        const searchUnmappedDesignation = await UnmappedDesignationModel.findOne({ designation: unmappedDesignation });
        console.log(searchUnmappedDesignation)

        searchMappedDesignation.mappedTo = [...searchMappedDesignation.mappedTo , unmappedDesignation];
        await searchMappedDesignation.save();

        searchUnmappedDesignation.isMapped = true;
        searchUnmappedDesignation.mappedWith = mappedDesignation;
        await searchUnmappedDesignation.save();

        await UserModel.updateMany(
            { designation: unmappedDesignation },
            { $set: { designation: mappedDesignation } }
        );

        return res.status(200).json({
            status: true,
            message: 'Designation is Successfully Mapped'
        })


    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message
        })
    }
}

// this code is responsible for unmapped the map designations which is mapped
exports.unmappedMappedDesignation = async (req, res) => {
    const { designation, mappedWith } = req.body;

    try {
        if (!designation || !mappedWith) {
            return res.status(400).json({
                status: false,
                message: 'Please provide both designation and mappedWith fields'
            });
        }

        const existedMappedDesignation = await UnmappedDesignationModel.findOne({ designation, mappedWith, isMapped: true });

        if (!existedMappedDesignation) {
            return res.status(404).json({
                status: false,
                message: 'Designation not found'
            });
        }

        // Update the unmapped Designation
        existedMappedDesignation.mappedWith = "";
        existedMappedDesignation.isMapped = false;
        await existedMappedDesignation.save();

        await DesignationMasterModel.updateMany(
            { designation: mappedWith },
            { $pull: { mappedTo: designation } }
        );

        // Update designation field in UserModel with userEnteredDesignation
        await UserModel.updateMany(
            { designation: mappedWith },
            [{ $set: { designation: "$userEnteredDesignation" } }]
        );

        return res.status(200).json({
            status: true,
            message: 'Designation unmapped successfully'
        });

    } catch (error) {
        return res.status(500).json({
            status: false,
            message: error.message
        });
    }
};


exports.getUnmappedDesignation = async ( req, res) => {
    try{
        const allUnmappedDesignation = await UnmappedDesignationModel.find();

        return res.status(200).json({
            status: true,
            data: allUnmappedDesignation,
            message: 'All Unmapped Designations'
        })
    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message
        })
    }
}

exports.deleteUnmappedDesignation = async (req, res) =>{
    const id = req.params.id;
    try{
        const designation = await UnmappedDesignationModel.findByIdAndDelete(id);
        if(!designation){
            return res.status(404).json({
                status: false,
                message: "Invalid Designation Id"
            })
        }

        return res.status(200).json({
            status: true,
            message: "Designation is deleted Successfully"
        })
    }catch(error){
        return res.status(404).json({
            status: false,
            message: error.message
        })
    }
}


// this controller is responsible to add the desgnation in the new pool if the designation is added in the others part whether it is from organiser panel , app platform
exports.addUnmappedOrNewDesignation = async(req, res) => {
    const { designation } = req.body;
    try{

        if(!designation){
            return res.status(404).json({
                status: false,
                message: 'Designation is required field'
            });
        }

        const existing = await UnmappedOrNewDesignationModel.findOne({ designation: designation.toLowerCase() });

        if(existing){
            return res.status(200).json({
                status: false,
                message: 'Designation is already Existed'
            });
        }

        const existingInDesignationMaster = await DesignationMasterModel.findOne({ designation: designation.toLowerCase() });

        if(existingInDesignationMaster){
            return res.status(200).json({
                status: false,
                message: 'Designation is already Existed in designation master'
            })
        }
        
        const newEntry = new UnmappedOrNewDesignationModel({ designation: designation.toLowerCase() });
        newEntry.save();

        return res.status(201).json({ 
            status: true,
            message: 'Added New Entry Successfully'
         });

    }catch(error){
        
        return res.status(500).json({
            status: false,
            message: error.message
        });
    }
}

// list all unmapped or new designation
exports.listAddUnmappedOrNewDesignation = async(req, res) => {
    try{
        const list = await UnmappedOrNewDesignationModel.find();

        return res.status(200).json({
            status: true,
            data: list,
            message: 'All Unmapped or New designation list'
        });

    }catch(error){
        return res.status(500).json({
            status: false,
            message: error.message
        });
    }
}

// delete the entry in unmapped or new designation
exports.deleteUnmappedOrNewDesignation = async (req, res) => {
    const {id} = req.params;
    try{

        if(!id){
            return res.status(404).json({
                status: false,
                message: 'Please provide the designation id'
            });
        }

        await UnmappedOrNewDesignationModel.findByIdAndDelete(id);

        return res.status(200).json({
            status: true,
            message: 'designation is deleted successfully'
        });

    }catch(error){
        return res.status(500).json({
            status: false,
            message: error.message
        })
    }
}

// send back the designation in pool once it got added in unmapped section
exports.unmappedToUnmappedOrNewDesignation = async (req, res) => {

    const unmapDesignation = req.body.designation.toLowerCase();

    try{

        const isExisting = await UnmappedOrNewDesignationModel.findOne({ designation: unmapDesignation });

        if(isExisting){

            const unmappedDesignation = await UnmappedDesignationModel.findOneAndDelete({ designation: unmapDesignation, isMapped: false });

            if(!unmappedDesignation){
                return res.status(404).json({
                    status: false,
                    message: 'Invalid designation'
                });
            }

            return res.status(200).json({
                status: true,
                message: 'Designation is already present in unmapped or new designation pool'
            });
        }else{
            const newEntry = new UnmappedOrNewDesignationModel({ designation: unmapDesignation });
            newEntry.save();

            const unmappedDesignation = await UnmappedDesignationModel.findOneAndDelete({ designation: unmapDesignation, isMapped: false });

            if(!unmappedDesignation){
                return res.status(404).json({
                    status: false,
                    message: 'Invalid designation'
                });
            }

            return res.status(201).json({
                status: true,
                message: 'Designation is added successfully to unmapped or new designation pool'
            });
        }

    }catch(error){
        return res.status(500).json({
            status: false,
            message: error.message
        })
    }
}

// send back the designation in a pool once it got added in new designation section
exports.designationToUnmappedOrNewDesignation = async (req, res) => {
    const designation = req.body.designation.toLowerCase();
    try{
        
        const exist = await UnmappedOrNewDesignationModel.findOne({ designation });
        if(!exist){

            const isPresentInDesignationMaster = await DesignationMasterModel.findOne({ designation });

            if(!isPresentInDesignationMaster){
                return res.status(404).json({
                    status: false,
                    message: 'Invalid Designation'
                })
            }

            const newEntry = new UnmappedOrNewDesignationModel({ designation });
            newEntry.save();

            await DesignationMasterModel.findOneAndDelete({ designation });

            return res.status(201).json({
                status: true,
                message: 'Added successfully to the unmapped or new designation pool'
            })
        }

        await DesignationMasterModel.findOneAndDelete({ designation });

        return res.status(404).json({
            status: false,
            message: 'Already present'
        })


    }catch(error){

        return res.status(500).json({
            status: false,
            message: error.message
        })

    }
}